<UserControl x:Class="SinterOptimizationClient.Views.OptimizationView"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    xmlns:views="clr-namespace:SinterOptimizationClient.Views" mc:Ignorable="d" d:DesignHeight="900" d:DesignWidth="1600">

    <Grid Background="{StaticResource LightGrayBrush}">
        <Grid.RowDefinitions>
            <!-- 物料信息表 - 占50%高度 -->
            <RowDefinition Height="0.5"/>
            <!-- 优化计算区 - 占50%高度 -->
            <RowDefinition Height="0.5*"/>
        </Grid.RowDefinitions>

        <!-- 物料信息表区域 -->
        <Border Grid.Row="0" Background="White" BorderBrush="{StaticResource BorderBrush}" BorderThickness="0,0,0,1">
            <views:MaterialDataView DataContext="{Binding MaterialDataViewModel}"/>
        </Border>

        <!-- 优化计算区 - 重新设计为规整的模块化布局 -->
        <Grid Grid.Row="1" Background="White" Margin="20">
            <Grid.RowDefinitions>
                <!-- 第一行：成分约束区 + 碱度约束区 -->
                <RowDefinition Height="Auto"/>
                <!-- 第二行：物料成本质量区 + 混配比例区 -->
                <RowDefinition Height="Auto"/>
                <!-- 第三行：配料优化目标区 -->
                <RowDefinition Height="*"/>
            </Grid.RowDefinitions>

            <!-- 第一行：成分约束区 + 碱度约束区 -->
            <Grid Grid.Row="0" Margin="0,0,0,20">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="0.6*"/>
                    <ColumnDefinition Width="0.4*"/>
                </Grid.ColumnDefinitions>

                <!-- 成分约束区 -->
                <Border Grid.Column="0" Style="{StaticResource IndustrialCardStyle}" Margin="0,0,10,0">
                    <StackPanel>
                        <TextBlock Text="成分约束区" Style="{StaticResource SectionTitleStyle}"/>

                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="80"/>
                                <ColumnDefinition Width="70"/>
                                <ColumnDefinition Width="70"/>
                                <ColumnDefinition Width="70"/>
                                <ColumnDefinition Width="70"/>
                                <ColumnDefinition Width="70"/>
                            </Grid.ColumnDefinitions>
                            <Grid.RowDefinitions>
                                <RowDefinition Height="30"/>
                                <RowDefinition Height="32"/>
                                <RowDefinition Height="32"/>
                                <RowDefinition Height="32"/>
                                <RowDefinition Height="32"/>
                                <RowDefinition Height="32"/>
                            </Grid.RowDefinitions>

                            <!-- 表头 -->
                            <TextBlock Grid.Row="0" Grid.Column="0" Text="成分" HorizontalAlignment="Center" FontSize="12" FontWeight="Bold"/>
                            <TextBlock Grid.Row="0" Grid.Column="1" Text="初始设定值" HorizontalAlignment="Center" FontSize="12" FontWeight="Bold"/>
                            <TextBlock Grid.Row="0" Grid.Column="2" Text="最小值" HorizontalAlignment="Center" FontSize="12" FontWeight="Bold"/>
                            <TextBlock Grid.Row="0" Grid.Column="3" Text="最大值" HorizontalAlignment="Center" FontSize="12" FontWeight="Bold"/>
                            <TextBlock Grid.Row="0" Grid.Column="4" Text="目标值" HorizontalAlignment="Center" FontSize="12" FontWeight="Bold"/>
                            <TextBlock Grid.Row="0" Grid.Column="5" Text="目标值偏差" HorizontalAlignment="Center" FontSize="12" FontWeight="Bold"/>

                            <!-- TFe -->
                            <TextBlock Grid.Row="1" Grid.Column="0" Text="TFe(%)" VerticalAlignment="Center" FontSize="11" FontWeight="Medium"/>
                            <TextBox Grid.Row="1" Grid.Column="1" Text="{Binding Parameters.TfeInitial, StringFormat=F1}" Style="{StaticResource InputTextBoxStyle}" Height="26" FontSize="11" Margin="2"/>
                            <TextBox Grid.Row="1" Grid.Column="2" Text="{Binding Parameters.TfeMin, StringFormat=F1}" Style="{StaticResource InputTextBoxStyle}" Height="26" FontSize="11" Margin="2"/>
                            <TextBox Grid.Row="1" Grid.Column="3" Text="{Binding Parameters.TfeMax, StringFormat=F1}" Style="{StaticResource InputTextBoxStyle}" Height="26" FontSize="11" Margin="2"/>
                            <TextBox Grid.Row="1" Grid.Column="4" Text="{Binding Parameters.TfeTarget, StringFormat=F1}" Style="{StaticResource InputTextBoxStyle}" Height="26" FontSize="11" Margin="2"/>
                            <TextBox Grid.Row="1" Grid.Column="5" Text="{Binding Parameters.TfeDeviation, StringFormat=F2}" Style="{StaticResource InputTextBoxStyle}" Height="26" FontSize="11" Margin="2"/>

                            <!-- SiO2 -->
                            <TextBlock Grid.Row="2" Grid.Column="0" Text="SiO₂(%)" VerticalAlignment="Center" FontSize="11" FontWeight="Medium"/>
                            <TextBox Grid.Row="2" Grid.Column="1" Text="{Binding Parameters.Sio2Initial, StringFormat=F1}" Style="{StaticResource InputTextBoxStyle}" Height="26" FontSize="11" Margin="2"/>
                            <TextBox Grid.Row="2" Grid.Column="2" Text="{Binding Parameters.Sio2Min, StringFormat=F1}" Style="{StaticResource InputTextBoxStyle}" Height="26" FontSize="11" Margin="2"/>
                            <TextBox Grid.Row="2" Grid.Column="3" Text="{Binding Parameters.Sio2Max, StringFormat=F1}" Style="{StaticResource InputTextBoxStyle}" Height="26" FontSize="11" Margin="2"/>
                            <TextBox Grid.Row="2" Grid.Column="4" Text="{Binding Parameters.Sio2Target, StringFormat=F1}" Style="{StaticResource InputTextBoxStyle}" Height="26" FontSize="11" Margin="2"/>
                            <TextBox Grid.Row="2" Grid.Column="5" Text="{Binding Parameters.Sio2Deviation, StringFormat=F2}" Style="{StaticResource InputTextBoxStyle}" Height="26" FontSize="11" Margin="2"/>

                            <!-- CaO -->
                            <TextBlock Grid.Row="3" Grid.Column="0" Text="CaO(%)" VerticalAlignment="Center" FontSize="11" FontWeight="Medium"/>
                            <TextBox Grid.Row="3" Grid.Column="1" Text="{Binding Parameters.CaoInitial, StringFormat=F1}" Style="{StaticResource InputTextBoxStyle}" Height="26" FontSize="11" Margin="2"/>
                            <TextBox Grid.Row="3" Grid.Column="2" Text="{Binding Parameters.CaoMin, StringFormat=F1}" Style="{StaticResource InputTextBoxStyle}" Height="26" FontSize="11" Margin="2"/>
                            <TextBox Grid.Row="3" Grid.Column="3" Text="{Binding Parameters.CaoMax, StringFormat=F1}" Style="{StaticResource InputTextBoxStyle}" Height="26" FontSize="11" Margin="2"/>
                            <TextBox Grid.Row="3" Grid.Column="4" Text="{Binding Parameters.CaoTarget, StringFormat=F1}" Style="{StaticResource InputTextBoxStyle}" Height="26" FontSize="11" Margin="2"/>
                            <TextBox Grid.Row="3" Grid.Column="5" Text="{Binding Parameters.CaoDeviation, StringFormat=F2}" Style="{StaticResource InputTextBoxStyle}" Height="26" FontSize="11" Margin="2"/>

                            <!-- MgO -->
                            <TextBlock Grid.Row="4" Grid.Column="0" Text="MgO(%)" VerticalAlignment="Center" FontSize="11" FontWeight="Medium"/>
                            <TextBox Grid.Row="4" Grid.Column="1" Text="{Binding Parameters.MgoInitial, StringFormat=F1}" Style="{StaticResource InputTextBoxStyle}" Height="26" FontSize="11" Margin="2"/>
                            <TextBox Grid.Row="4" Grid.Column="2" Text="{Binding Parameters.MgoMin, StringFormat=F1}" Style="{StaticResource InputTextBoxStyle}" Height="26" FontSize="11" Margin="2"/>
                            <TextBox Grid.Row="4" Grid.Column="3" Text="{Binding Parameters.MgoMax, StringFormat=F1}" Style="{StaticResource InputTextBoxStyle}" Height="26" FontSize="11" Margin="2"/>
                            <TextBox Grid.Row="4" Grid.Column="4" Text="{Binding Parameters.MgoTarget, StringFormat=F1}" Style="{StaticResource InputTextBoxStyle}" Height="26" FontSize="11" Margin="2"/>
                            <TextBox Grid.Row="4" Grid.Column="5" Text="{Binding Parameters.MgoDeviation, StringFormat=F2}" Style="{StaticResource InputTextBoxStyle}" Height="26" FontSize="11" Margin="2"/>

                            <!-- Al2O3 -->
                            <TextBlock Grid.Row="5" Grid.Column="0" Text="Al₂O₃(%)" VerticalAlignment="Center" FontSize="11" FontWeight="Medium"/>
                            <TextBox Grid.Row="5" Grid.Column="1" Text="{Binding Parameters.Al2o3Initial, StringFormat=F1}" Style="{StaticResource InputTextBoxStyle}" Height="26" FontSize="11" Margin="2"/>
                            <TextBox Grid.Row="5" Grid.Column="2" Text="{Binding Parameters.Al2o3Min, StringFormat=F1}" Style="{StaticResource InputTextBoxStyle}" Height="26" FontSize="11" Margin="2"/>
                            <TextBox Grid.Row="5" Grid.Column="3" Text="{Binding Parameters.Al2o3Max, StringFormat=F1}" Style="{StaticResource InputTextBoxStyle}" Height="26" FontSize="11" Margin="2"/>
                            <TextBox Grid.Row="5" Grid.Column="4" Text="{Binding Parameters.Al2o3Target, StringFormat=F1}" Style="{StaticResource InputTextBoxStyle}" Height="26" FontSize="11" Margin="2"/>
                            <TextBox Grid.Row="5" Grid.Column="5" Text="{Binding Parameters.Al2o3Deviation, StringFormat=F2}" Style="{StaticResource InputTextBoxStyle}" Height="26" FontSize="11" Margin="2"/>
                        </Grid>
                    </StackPanel>
                </Border>

                <!-- 碱度约束区 -->
                <Border Grid.Column="1" Style="{StaticResource IndustrialCardStyle}" Margin="10,0,0,0">
                    <StackPanel>
                        <TextBlock Text="碱度约束区" Style="{StaticResource SectionTitleStyle}"/>

                        <StackPanel Margin="0,0,0,12">
                            <RadioButton Content="Ro=CaO/SiO₂" FontSize="12" IsChecked="{Binding Parameters.RoCalculationType, Converter={StaticResource EnumToBooleanConverter}, ConverterParameter=CaoSio2}" Margin="0,0,0,6"/>
                            <RadioButton Content="Ro=(CaO+MgO)/(SiO₂+Al₂O₃)" FontSize="12" IsChecked="{Binding Parameters.RoCalculationType, Converter={StaticResource EnumToBooleanConverter}, ConverterParameter=CaoMgoSio2Al2o3}"/>
                        </StackPanel>

                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="80"/>
                                <ColumnDefinition Width="70"/>
                                <ColumnDefinition Width="70"/>
                                <ColumnDefinition Width="70"/>
                                <ColumnDefinition Width="70"/>
                            </Grid.ColumnDefinitions>
                            <Grid.RowDefinitions>
                                <RowDefinition Height="30"/>
                                <RowDefinition Height="32"/>
                            </Grid.RowDefinitions>

                            <!-- 表头 -->
                            <TextBlock Grid.Row="0" Grid.Column="0" Text="碱度" HorizontalAlignment="Center" FontSize="12" FontWeight="Bold"/>
                            <TextBlock Grid.Row="0" Grid.Column="1" Text="初始设定值" HorizontalAlignment="Center" FontSize="12" FontWeight="Bold"/>
                            <TextBlock Grid.Row="0" Grid.Column="2" Text="最小值" HorizontalAlignment="Center" FontSize="12" FontWeight="Bold"/>
                            <TextBlock Grid.Row="0" Grid.Column="3" Text="最大值" HorizontalAlignment="Center" FontSize="12" FontWeight="Bold"/>
                            <TextBlock Grid.Row="0" Grid.Column="4" Text="目标值" HorizontalAlignment="Center" FontSize="12" FontWeight="Bold"/>

                            <!-- Ro -->
                            <TextBlock Grid.Row="1" Grid.Column="0" Text="Ro" VerticalAlignment="Center" FontSize="11" FontWeight="Medium"/>
                            <TextBox Grid.Row="1" Grid.Column="1" Text="{Binding Parameters.RoInitial, StringFormat=F2}" Style="{StaticResource InputTextBoxStyle}" Height="26" FontSize="11" Margin="2"/>
                            <TextBox Grid.Row="1" Grid.Column="2" Text="{Binding Parameters.RoMin, StringFormat=F2}" Style="{StaticResource InputTextBoxStyle}" Height="26" FontSize="11" Margin="2"/>
                            <TextBox Grid.Row="1" Grid.Column="3" Text="{Binding Parameters.RoMax, StringFormat=F2}" Style="{StaticResource InputTextBoxStyle}" Height="26" FontSize="11" Margin="2"/>
                            <TextBox Grid.Row="1" Grid.Column="4" Text="{Binding Parameters.RoTarget, StringFormat=F2}" Style="{StaticResource InputTextBoxStyle}" Height="26" FontSize="11" Margin="2"/>
                        </Grid>

                        <!-- 更新确认按钮 -->
                        <Button Content="更新确认" Style="{StaticResource SuccessButtonStyle}" Command="{Binding UpdateConstraintsCommand}" Width="120" Height="32" HorizontalAlignment="Center" Margin="0,15,0,0"/>
                    </StackPanel>
                </Border>
            </Grid>

            <!-- 第二行：物料成本质量区 + 混配比例区 -->
            <Grid Grid.Row="1" Margin="0,0,0,20">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="0.5*"/>
                    <ColumnDefinition Width="0.5*"/>
                </Grid.ColumnDefinitions>

                <!-- 物料成本质量区 -->
                <Border Grid.Column="0" Style="{StaticResource IndustrialCardStyle}" Margin="0,0,10,0">
                    <StackPanel>
                        <TextBlock Text="物料成本质量区" Style="{StaticResource SectionTitleStyle}"/>

                        <!-- 物料初选功能直接显示 -->
                        <StackPanel Margin="0,0,0,15">
                            <TextBlock Text="物料初选功能" FontSize="12" FontWeight="Bold" Margin="0,0,0,8"/>

                            <!-- 物料选择列表 -->
                            <ScrollViewer MaxHeight="120" VerticalScrollBarVisibility="Auto">
                                <ItemsControl ItemsSource="{Binding AvailableMaterials}">
                                    <ItemsControl.ItemTemplate>
                                        <DataTemplate>
                                            <Grid Margin="0,2">
                                                <Grid.ColumnDefinitions>
                                                    <ColumnDefinition Width="25"/>
                                                    <ColumnDefinition Width="80"/>
                                                    <ColumnDefinition Width="60"/>
                                                    <ColumnDefinition Width="80"/>
                                                </Grid.ColumnDefinitions>

                                                <CheckBox Grid.Column="0" IsChecked="{Binding IsSelected}" VerticalAlignment="Center"/>
                                                <TextBlock Grid.Column="1" Text="{Binding Name}" VerticalAlignment="Center" FontSize="10"/>
                                                <Border Grid.Column="2" Background="{Binding CategoryColor}" CornerRadius="8" Padding="4,2" Margin="2">
                                                    <TextBlock Text="{Binding Category}" FontSize="9" Foreground="White" HorizontalAlignment="Center"/>
                                                </Border>
                                                <TextBlock Grid.Column="3" Text="{Binding Price, StringFormat='{}{0:F0}元/吨'}" VerticalAlignment="Center" FontSize="9" Foreground="#6B7280"/>
                                            </Grid>
                                        </DataTemplate>
                                    </ItemsControl.ItemTemplate>
                                </ItemsControl>
                            </ScrollViewer>
                        </StackPanel>

                        <!-- 成本与质量目标设定 -->
                        <StackPanel>
                            <TextBlock Text="成本与质量目标设定" FontSize="12" FontWeight="Bold" Margin="0,0,0,8"/>

                            <Grid>
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="120"/>
                                    <ColumnDefinition Width="80"/>
                                    <ColumnDefinition Width="40"/>
                                </Grid.ColumnDefinitions>
                                <Grid.RowDefinitions>
                                    <RowDefinition Height="32"/>
                                    <RowDefinition Height="32"/>
                                    <RowDefinition Height="32"/>
                                </Grid.RowDefinitions>

                                <TextBlock Grid.Row="0" Grid.Column="0" Text="成本上限(元/吨):" VerticalAlignment="Center" FontSize="11"/>
                                <TextBox Grid.Row="0" Grid.Column="1" Text="{Binding Parameters.CostTargetMax, StringFormat=F0}" Style="{StaticResource InputTextBoxStyle}" Height="26" FontSize="11" Margin="2"/>

                                <TextBlock Grid.Row="1" Grid.Column="0" Text="TFe最大偏差(%):" VerticalAlignment="Center" FontSize="11"/>
                                <TextBox Grid.Row="1" Grid.Column="1" Text="{Binding Parameters.TfeMaxDeviation, StringFormat=F2}" Style="{StaticResource InputTextBoxStyle}" Height="26" FontSize="11" Margin="2" IsEnabled="{Binding Parameters.OptimizationType, Converter={StaticResource EnumToBooleanConverter}, ConverterParameter=QualityOptimal}"/>

                                <TextBlock Grid.Row="2" Grid.Column="0" Text="Ro最大偏差(%):" VerticalAlignment="Center" FontSize="11"/>
                                <TextBox Grid.Row="2" Grid.Column="1" Text="{Binding Parameters.RoMaxDeviation, StringFormat=F3}" Style="{StaticResource InputTextBoxStyle}" Height="26" FontSize="11" Margin="2" IsEnabled="{Binding Parameters.OptimizationType, Converter={StaticResource EnumToBooleanConverter}, ConverterParameter=QualityOptimal}"/>
                            </Grid>
                        </StackPanel>
                    </StackPanel>
                </Border>

                <!-- 混配比例区 -->
                <Border Grid.Column="1" Style="{StaticResource IndustrialCardStyle}" Margin="10,0,0,0">
                    <StackPanel>
                        <TextBlock Text="混配比例区" Style="{StaticResource SectionTitleStyle}"/>

                        <Button Content="统一设置混配比例范围" Style="{StaticResource PrimaryButtonStyle}" Command="{Binding SetUniformRatioRangeCommand}" Width="180" Height="32" HorizontalAlignment="Left" Margin="0,0,0,12"/>

                        <ScrollViewer MaxHeight="200" VerticalScrollBarVisibility="Auto">
                            <ItemsControl ItemsSource="{Binding Materials}">
                                <ItemsControl.ItemTemplate>
                                    <DataTemplate>
                                        <Grid Margin="0,2">
                                            <Grid.ColumnDefinitions>
                                                <ColumnDefinition Width="25"/>
                                                <ColumnDefinition Width="80"/>
                                                <ColumnDefinition Width="50"/>
                                                <ColumnDefinition Width="20"/>
                                                <ColumnDefinition Width="50"/>
                                                <ColumnDefinition Width="20"/>
                                                <ColumnDefinition Width="80"/>
                                            </Grid.ColumnDefinitions>

                                            <CheckBox Grid.Column="0" IsChecked="{Binding IsSelected}" VerticalAlignment="Center"/>
                                            <TextBlock Grid.Column="1" Text="{Binding Name}" VerticalAlignment="Center" FontSize="10"/>
                                            <Border Grid.Column="1" Background="{Binding CategoryColor}" CornerRadius="8" Padding="4,2" Margin="0,0,4,0" HorizontalAlignment="Right">
                                                <TextBlock Text="{Binding CategoryShort}" FontSize="8" Foreground="White"/>
                                            </Border>
                                            <TextBox Grid.Column="2" Text="{Binding MinRatio, StringFormat=F1}" Style="{StaticResource InputTextBoxStyle}" Height="24" FontSize="11" Margin="1"/>
                                            <TextBlock Grid.Column="3" Text="~" VerticalAlignment="Center" HorizontalAlignment="Center" FontSize="11"/>
                                            <TextBox Grid.Column="4" Text="{Binding MaxRatio, StringFormat=F1}" Style="{StaticResource InputTextBoxStyle}" Height="24" FontSize="11" Margin="1"/>
                                            <TextBlock Grid.Column="5" Text="%" VerticalAlignment="Center" FontSize="10"/>
                                            <TextBlock Grid.Column="6" Text="{Binding Price, StringFormat='{}{0:F0}元/吨'}" VerticalAlignment="Center" FontSize="9" Foreground="#6B7280"/>
                                        </Grid>
                                    </DataTemplate>
                                </ItemsControl.ItemTemplate>
                            </ItemsControl>
                        </ScrollViewer>
                    </StackPanel>
                </Border>
            </Grid>

            <!-- 第三行：配料优化目标区 -->
            <Border Grid.Row="2" Style="{StaticResource IndustrialCardStyle}" Margin="0">
                <Grid>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="0.3*"/>
                        <ColumnDefinition Width="0.4*"/>
                        <ColumnDefinition Width="0.3*"/>
                    </Grid.ColumnDefinitions>

                    <!-- 左列：配料优化目标选择 -->
                    <StackPanel Grid.Column="0" Margin="0,0,15,0">
                        <TextBlock Text="配料优化目标" Style="{StaticResource SectionTitleStyle}"/>

                        <!-- 优化目标选择 -->
                        <StackPanel Margin="0,0,0,15">
                            <RadioButton Content="成本最优" FontSize="12" IsChecked="{Binding Parameters.OptimizationType, Converter={StaticResource EnumToBooleanConverter}, ConverterParameter=CostOptimal}" Margin="0,0,0,8">
                                <RadioButton.ToolTip>
                                    <ToolTip Content="优先降低吨矿成本，在约束条件范围内寻找最低成本解"/>
                                </RadioButton.ToolTip>
                            </RadioButton>
                            <TextBlock Text="• 优先降低吨矿成本" FontSize="10" Foreground="#6B7280" Margin="18,0,0,4"/>
                            <TextBlock Text="• 适当放宽约束条件" FontSize="10" Foreground="#6B7280" Margin="18,0,0,4"/>
                            <TextBlock Text="• 成本导向优化策略" FontSize="10" Foreground="#6B7280" Margin="18,0,0,12"/>

                            <RadioButton Content="质量最优" FontSize="12" IsChecked="{Binding Parameters.OptimizationType, Converter={StaticResource EnumToBooleanConverter}, ConverterParameter=QualityOptimal}" Margin="0,0,0,8">
                                <RadioButton.ToolTip>
                                    <ToolTip Content="优先缩小成分偏差，严格按照约束条件寻找最优质量解"/>
                                </RadioButton.ToolTip>
                            </RadioButton>
                            <TextBlock Text="• 优先缩小成分偏差" FontSize="10" Foreground="#6B7280" Margin="18,0,0,4"/>
                            <TextBlock Text="• 严格约束条件控制" FontSize="10" Foreground="#6B7280" Margin="18,0,0,4"/>
                            <TextBlock Text="• 质量导向优化策略" FontSize="10" Foreground="#6B7280" Margin="18,0,0,12"/>
                        </StackPanel>

                        <!-- 算法选择 -->
                        <StackPanel>
                            <TextBlock Text="优化算法选择" FontSize="12" FontWeight="Bold" Margin="0,0,0,8"/>
                            <ComboBox SelectedItem="{Binding Parameters.AlgorithmType}" FontSize="11" Height="28" Margin="0,0,0,6">
                                <ComboBoxItem Content="SQP二次序列算法"/>
                                <ComboBoxItem Content="遗传算法（预留）" IsEnabled="False"/>
                                <ComboBoxItem Content="粒子群算法（预留）" IsEnabled="False"/>
                            </ComboBox>
                            <TextBlock Text="• SQP算法，精度±0.01" FontSize="10" Foreground="#6B7280" Margin="0,0,0,2"/>
                            <TextBlock Text="• 适用于非线性约束优化" FontSize="10" Foreground="#6B7280" Margin="0,0,0,2"/>
                            <TextBlock Text="• 收敛速度快，精度高" FontSize="10" Foreground="#6B7280"/>
                        </StackPanel>
                    </StackPanel>

                    <!-- 中列：生产参数设置 -->
                    <StackPanel Grid.Column="1" Margin="15,0">
                        <TextBlock Text="生产参数设置" Style="{StaticResource SectionTitleStyle}"/>

                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="100"/>
                                <ColumnDefinition Width="80"/>
                                <ColumnDefinition Width="30"/>
                            </Grid.ColumnDefinitions>
                            <Grid.RowDefinitions>
                                <RowDefinition Height="32"/>
                                <RowDefinition Height="32"/>
                                <RowDefinition Height="32"/>
                                <RowDefinition Height="32"/>
                            </Grid.RowDefinitions>

                            <TextBlock Grid.Row="0" Grid.Column="0" Text="计划日产:" VerticalAlignment="Center" FontSize="11"/>
                            <TextBox Grid.Row="0" Grid.Column="1" Text="{Binding Parameters.PlannedDailyOutput, StringFormat=F0}" Style="{StaticResource InputTextBoxStyle}" Height="26" FontSize="11" Margin="2"/>
                            <TextBlock Grid.Row="0" Grid.Column="2" Text="吨" VerticalAlignment="Center" FontSize="11" Margin="5,0,0,0"/>

                            <TextBlock Grid.Row="1" Grid.Column="0" Text="金属收得率:" VerticalAlignment="Center" FontSize="11"/>
                            <TextBox Grid.Row="1" Grid.Column="1" Text="{Binding Parameters.MetalRecoveryRate, StringFormat=F1}" Style="{StaticResource InputTextBoxStyle}" Height="26" FontSize="11" Margin="2"/>
                            <TextBlock Grid.Row="1" Grid.Column="2" Text="%" VerticalAlignment="Center" FontSize="11" Margin="5,0,0,0"/>

                            <TextBlock Grid.Row="2" Grid.Column="0" Text="出矿率:" VerticalAlignment="Center" FontSize="11"/>
                            <TextBox Grid.Row="2" Grid.Column="1" Text="{Binding Parameters.SinterYield, StringFormat=F1}" Style="{StaticResource InputTextBoxStyle}" Height="26" FontSize="11" Margin="2"/>
                            <TextBlock Grid.Row="2" Grid.Column="2" Text="%" VerticalAlignment="Center" FontSize="11" Margin="5,0,0,0"/>

                            <TextBlock Grid.Row="3" Grid.Column="0" Text="作业率:" VerticalAlignment="Center" FontSize="11"/>
                            <TextBox Grid.Row="3" Grid.Column="1" Text="{Binding Parameters.OperationRate, StringFormat=F1}" Style="{StaticResource InputTextBoxStyle}" Height="26" FontSize="11" Margin="2"/>
                            <TextBlock Grid.Row="3" Grid.Column="2" Text="%" VerticalAlignment="Center" FontSize="11" Margin="5,0,0,0"/>
                        </Grid>

                        <!-- 计算进度显示 -->
                        <StackPanel Visibility="{Binding IsCalculating, Converter={StaticResource BooleanToVisibilityConverter}}" Margin="0,15,0,0">
                            <TextBlock Text="算法计算进度" FontSize="12" FontWeight="Bold" Margin="0,0,0,8"/>
                            <ProgressBar Value="{Binding CalculationProgress}" Height="24" Margin="0,0,0,6"/>
                            <TextBlock Text="{Binding StatusMessage}" HorizontalAlignment="Center" FontSize="11" Foreground="#6B7280"/>
                        </StackPanel>
                    </StackPanel>

                    <!-- 右列：算法计算控制 -->
                    <StackPanel Grid.Column="2" Margin="15,0,0,0">
                        <TextBlock Text="算法计算优化" Style="{StaticResource SectionTitleStyle}"/>

                        <!-- 主要计算按钮 -->
                        <Button Content="🔥 启动算法计算" Style="{StaticResource PrimaryButtonStyle}" Command="{Binding StartOptimizationCalculationCommand}" IsEnabled="{Binding IsCalculating, Converter={StaticResource InverseBooleanConverter}}" Width="160" Height="45" FontSize="13" FontWeight="Bold" HorizontalAlignment="Center" Margin="0,0,0,15"/>

                        <!-- 辅助按钮组 -->
                        <StackPanel>
                            <Button Content="验证约束条件" Style="{StaticResource SuccessButtonStyle}" Command="{Binding ValidateConstraintsCommand}" Width="140" Height="32" FontSize="11" HorizontalAlignment="Center" Margin="0,0,0,8"/>

                            <Button Content="重置所有参数" Style="{StaticResource SecondaryButtonStyle}" Command="{Binding ResetAllParametersCommand}" Width="140" Height="32" FontSize="11" HorizontalAlignment="Center" Margin="0,0,0,8"/>

                            <Button Content="保存当前配置" Style="{StaticResource SecondaryButtonStyle}" Command="{Binding SaveCurrentConfigCommand}" Width="140" Height="32" FontSize="11" HorizontalAlignment="Center" Margin="0,0,0,15"/>
                        </StackPanel>

                        <!-- 计算状态指示 -->
                        <Border Background="#F3F4F6" CornerRadius="8" Padding="12" Margin="0,0,0,0">
                            <StackPanel>
                                <TextBlock Text="计算状态" FontSize="11" FontWeight="Bold" Margin="0,0,0,6"/>
                                <StackPanel Orientation="Horizontal" Margin="0,0,0,4">
                                    <Ellipse Width="8" Height="8" Margin="0,0,6,0">
                                        <Ellipse.Fill>
                                            <SolidColorBrush Color="{Binding CalculationStatusColor}"/>
                                        </Ellipse.Fill>
                                    </Ellipse>
                                    <TextBlock Text="{Binding CalculationStatusText}" FontSize="10"/>
                                </StackPanel>
                                <TextBlock Text="{Binding LastCalculationTime, StringFormat='上次计算: {0:HH:mm:ss}'}" FontSize="9" Foreground="#6B7280"/>
                            </StackPanel>
                        </Border>
                    </StackPanel>
                </Grid>
            </Border>

        </Grid>
    </Grid>



<!-- SiO2 -->
<TextBlock Grid.Row="2" Grid.Column="0" Text="SiO₂(%):" VerticalAlignment="Center" FontSize="11"/>
<TextBox Grid.Row="2" Grid.Column="1" Text="{Binding Parameters.Sio2Min, StringFormat=F1}" Height="22" FontSize="11" Padding="3,2" Margin="2,1"/>
<TextBlock Grid.Row="2" Grid.Column="2" Text="~" VerticalAlignment="Center" HorizontalAlignment="Center" FontSize="11"/>
<TextBox Grid.Row="2" Grid.Column="3" Text="{Binding Parameters.Sio2Max, StringFormat=F1}" Height="22" FontSize="11" Padding="3,2" Margin="2,1"/>
<TextBlock Grid.Row="2" Grid.Column="4" Text="目标:" VerticalAlignment="Center" HorizontalAlignment="Center" FontSize="10"/>
<TextBox Grid.Row="2" Grid.Column="5" Text="{Binding Parameters.Sio2Target, StringFormat=F1}" Height="22" FontSize="11" Padding="3,2" Margin="2,1"/>

<!-- CaO -->
<TextBlock Grid.Row="3" Grid.Column="0" Text="CaO(%):" VerticalAlignment="Center" FontSize="11"/>
<TextBox Grid.Row="3" Grid.Column="1" Text="{Binding Parameters.CaoMin, StringFormat=F1}" Height="22" FontSize="11" Padding="3,2" Margin="2,1"/>
<TextBlock Grid.Row="3" Grid.Column="2" Text="~" VerticalAlignment="Center" HorizontalAlignment="Center" FontSize="11"/>
<TextBox Grid.Row="3" Grid.Column="3" Text="{Binding Parameters.CaoMax, StringFormat=F1}" Height="22" FontSize="11" Padding="3,2" Margin="2,1"/>
<TextBlock Grid.Row="3" Grid.Column="4" Text="目标:" VerticalAlignment="Center" HorizontalAlignment="Center" FontSize="10"/>
<TextBox Grid.Row="3" Grid.Column="5" Text="{Binding Parameters.CaoTarget, StringFormat=F1}" Height="22" FontSize="11" Padding="3,2" Margin="2,1"/>

<!-- MgO -->
<TextBlock Grid.Row="4" Grid.Column="0" Text="MgO(%):" VerticalAlignment="Center" FontSize="11"/>
<TextBox Grid.Row="4" Grid.Column="1" Text="{Binding Parameters.MgoMin, StringFormat=F1}" Height="22" FontSize="11" Padding="3,2" Margin="2,1"/>
<TextBlock Grid.Row="4" Grid.Column="2" Text="~" VerticalAlignment="Center" HorizontalAlignment="Center" FontSize="11"/>
<TextBox Grid.Row="4" Grid.Column="3" Text="{Binding Parameters.MgoMax, StringFormat=F1}" Height="22" FontSize="11" Padding="3,2" Margin="2,1"/>
<TextBlock Grid.Row="4" Grid.Column="4" Text="目标:" VerticalAlignment="Center" HorizontalAlignment="Center" FontSize="10"/>
<TextBox Grid.Row="4" Grid.Column="5" Text="{Binding Parameters.MgoTarget, StringFormat=F1}" Height="22" FontSize="11" Padding="3,2" Margin="2,1"/>

<!-- Al2O3 -->
<TextBlock Grid.Row="5" Grid.Column="0" Text="Al₂O₃(%):" VerticalAlignment="Center" FontSize="11"/>
<TextBox Grid.Row="5" Grid.Column="1" Text="{Binding Parameters.Al2o3Min, StringFormat=F1}" Height="22" FontSize="11" Padding="3,2" Margin="2,1"/>
<TextBlock Grid.Row="5" Grid.Column="2" Text="~" VerticalAlignment="Center" HorizontalAlignment="Center" FontSize="11"/>
<TextBox Grid.Row="5" Grid.Column="3" Text="{Binding Parameters.Al2o3Max, StringFormat=F1}" Height="22" FontSize="11" Padding="3,2" Margin="2,1"/>
<TextBlock Grid.Row="5" Grid.Column="4" Text="目标:" VerticalAlignment="Center" HorizontalAlignment="Center" FontSize="10"/>
<TextBox Grid.Row="5" Grid.Column="5" Text="{Binding Parameters.Al2o3Target, StringFormat=F1}" Height="22" FontSize="11" Padding="3,2" Margin="2,1"/>
</Grid>
</StackPanel>

<!-- 模块2：碱度约束区 -->
<StackPanel Margin="0,0,0,15">
<TextBlock Text="碱度约束区" FontSize="14" FontWeight="Bold" Foreground="{StaticResource PrimaryBrush}" Margin="0,0,0,10"/>

<StackPanel Margin="0,0,0,8">
<RadioButton Content="Ro=CaO/SiO₂" FontSize="11" IsChecked="{Binding Parameters.RoCalculationType, Converter={StaticResource EnumToBooleanConverter}, ConverterParameter=CaoSio2}" Margin="0,0,0,3"/>
<RadioButton Content="Ro=(CaO+MgO)/(SiO₂+Al₂O₃)" FontSize="11" IsChecked="{Binding Parameters.RoCalculationType, Converter={StaticResource EnumToBooleanConverter}, ConverterParameter=CaoMgoSio2Al2o3}"/>
</StackPanel>

<Grid>
<Grid.ColumnDefinitions>
    <ColumnDefinition Width="60"/>
    <ColumnDefinition Width="60"/>
    <ColumnDefinition Width="15"/>
    <ColumnDefinition Width="60"/>
    <ColumnDefinition Width="35"/>
    <ColumnDefinition Width="60"/>
</Grid.ColumnDefinitions>

<TextBlock Grid.Column="0" Text="Ro范围:" VerticalAlignment="Center" FontSize="11"/>
<TextBox Grid.Column="1" Text="{Binding Parameters.RoMin, StringFormat=F2}" Height="22" FontSize="11" Padding="3,2" Margin="2,1"/>
<TextBlock Grid.Column="2" Text="~" VerticalAlignment="Center" HorizontalAlignment="Center" FontSize="11"/>
<TextBox Grid.Column="3" Text="{Binding Parameters.RoMax, StringFormat=F2}" Height="22" FontSize="11" Padding="3,2" Margin="2,1"/>
<TextBlock Grid.Column="4" Text="目标:" VerticalAlignment="Center" HorizontalAlignment="Center" FontSize="10"/>
<TextBox Grid.Column="5" Text="{Binding Parameters.RoTarget, StringFormat=F2}" Height="22" FontSize="11" Padding="3,2" Margin="2,1"/>
</Grid>
</StackPanel>
</StackPanel>

<!-- 右列：混配比例区 -->
<StackPanel Grid.Column="1" Margin="0,0,0,0">
<TextBlock Text="混配比例区" FontSize="14" FontWeight="Bold" Foreground="{StaticResource PrimaryBrush}" Margin="0,0,0,10"/>

<Button Content="统一设置湿配比范围" Background="{StaticResource PrimaryBrush}" Foreground="White" FontSize="11" Height="26" Command="{Binding SetUniformRatioRangeCommand}" HorizontalAlignment="Left" Margin="0,0,0,8"/>

<ScrollViewer MaxHeight="180" VerticalScrollBarVisibility="Auto">
<ItemsControl ItemsSource="{Binding Materials}">
<ItemsControl.ItemTemplate>
    <DataTemplate>
        <Grid Margin="0,1">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="80"/>
                <ColumnDefinition Width="50"/>
                <ColumnDefinition Width="15"/>
                <ColumnDefinition Width="50"/>
                <ColumnDefinition Width="15"/>
            </Grid.ColumnDefinitions>

            <TextBlock Grid.Column="0" Text="{Binding Name}" VerticalAlignment="Center" FontSize="10"/>
            <TextBox Grid.Column="1" Text="{Binding MinRatio, StringFormat=F1}" Height="22" FontSize="11" Padding="3,2" Margin="1"/>
            <TextBlock Grid.Column="2" Text="~" VerticalAlignment="Center" HorizontalAlignment="Center" FontSize="11"/>
            <TextBox Grid.Column="3" Text="{Binding MaxRatio, StringFormat=F1}" Height="22" FontSize="11" Padding="3,2" Margin="1"/>
            <TextBlock Grid.Column="4" Text="%" VerticalAlignment="Center" FontSize="10"/>
        </Grid>
    </DataTemplate>
</ItemsControl.ItemTemplate>
</ItemsControl>
</ScrollViewer>
</StackPanel>
</Grid>

<!-- 模块3：物料&成本质量区 -->
<StackPanel Margin="0,0,0,15">
<TextBlock Text="物料成本质量区" FontSize="14" FontWeight="Bold" Foreground="{StaticResource PrimaryBrush}" Margin="0,0,0,10"/>

<!-- 物料初选功能 -->
<StackPanel Margin="0,0,0,10">
<TextBlock Text="物料初选功能" FontSize="12" FontWeight="Bold" Margin="0,0,0,6"/>
<Button Content="物料选择" Background="{StaticResource OrangeBrush}" Foreground="White" FontSize="11" Height="28" Command="{Binding ShowMaterialSelectionCommand}" HorizontalAlignment="Left" Margin="0,0,0,6"/>
</StackPanel>

<!-- 成本与质量目标设定 -->
<StackPanel>
<TextBlock Text="成本与质量目标设定" FontSize="12" FontWeight="Bold" Margin="0,0,0,8"/>

<Grid>
<Grid.ColumnDefinitions>
<ColumnDefinition Width="120"/>
<ColumnDefinition Width="80"/>
<ColumnDefinition Width="40"/>
</Grid.ColumnDefinitions>
<Grid.RowDefinitions>
<RowDefinition Height="26"/>
<RowDefinition Height="26"/>
<RowDefinition Height="26"/>
</Grid.RowDefinitions>

<TextBlock Grid.Row="0" Grid.Column="0" Text="成本上限(元/吨):" VerticalAlignment="Center" FontSize="11"/>
<TextBox Grid.Row="0" Grid.Column="1" Text="{Binding Parameters.CostTargetMax, StringFormat=F0}" Height="22" FontSize="11" Padding="3,2" Margin="2,1"/>

<TextBlock Grid.Row="1" Grid.Column="0" Text="TFe最大偏差(%):" VerticalAlignment="Center" FontSize="11"/>
<TextBox Grid.Row="1" Grid.Column="1" Text="{Binding Parameters.TfeMaxDeviation, StringFormat=F2}" Height="22" FontSize="11" Padding="3,2" Margin="2,1" IsEnabled="{Binding Parameters.OptimizationType, Converter={StaticResource EnumToBooleanConverter}, ConverterParameter=QualityOptimal}"/>

<TextBlock Grid.Row="2" Grid.Column="0" Text="Ro最大偏差(%):" VerticalAlignment="Center" FontSize="11"/>
<TextBox Grid.Row="2" Grid.Column="1" Text="{Binding Parameters.RoMaxDeviation, StringFormat=F3}" Height="22" FontSize="11" Padding="3,2" Margin="2,1" IsEnabled="{Binding Parameters.OptimizationType, Converter={StaticResource EnumToBooleanConverter}, ConverterParameter=QualityOptimal}"/>
</Grid>
</StackPanel>
</StackPanel>

<!-- 操作按钮区域 -->
<StackPanel Margin="0,20,0,0">
<!-- 约束条件确认按钮 -->
<Button Content="确定约束条件" Style="{StaticResource PrimaryButtonStyle}" Command="{Binding ConfirmConstraintsCommand}" Width="150" Height="35" HorizontalAlignment="Center" Background="#10B981" Margin="0,0,0,10"/>

<!-- 计算按钮 -->
<Button Content="开始计算" Style="{StaticResource PrimaryButtonStyle}" Command="{Binding StartCalculationCommand}" IsEnabled="{Binding IsCalculating, Converter={StaticResource InverseBooleanConverter}}" Width="150" Height="40" HorizontalAlignment="Center" Margin="0,0,0,10"/>

<!-- 辅助按钮 -->
<StackPanel Orientation="Horizontal" HorizontalAlignment="Center" Margin="0,0,0,10">
<Button Content="验证约束" Style="{StaticResource PrimaryButtonStyle}" Command="{Binding ValidateConstraintsCommand}" Width="70" Height="30" FontSize="10" Margin="0,0,10,0"/>
<Button Content="重置参数" Style="{StaticResource PrimaryButtonStyle}" Command="{Binding ResetParametersCommand}" Width="70" Height="30" FontSize="10"/>
</StackPanel>

<!-- 计算进度 -->
<StackPanel Visibility="{Binding IsCalculating, Converter={StaticResource BooleanToVisibilityConverter}}" Margin="0,10,0,0">
<ProgressBar Value="{Binding CalculationProgress}" Height="20" Margin="0,0,0,5"/>
<TextBlock Text="{Binding StatusMessage}" HorizontalAlignment="Center" FontSize="12"/>
</StackPanel>
</StackPanel>

</StackPanel>
</Border>

</Grid>
</Grid>
</UserControl>
