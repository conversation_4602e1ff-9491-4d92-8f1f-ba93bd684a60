<Application x:Class="SinterOptimizationClient.App"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:local="clr-namespace:SinterOptimizationClient.Converters"
             StartupUri="MainWindow.xaml">
    <Application.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <!-- 自定义样式 -->
                <ResourceDictionary Source="Styles/AppStyles.xaml" />
            </ResourceDictionary.MergedDictionaries>

            <!-- 转换器 -->
            <local:BooleanToVisibilityConverter x:Key="BooleanToVisibilityConverter"/>
            <local:InverseBooleanConverter x:Key="InverseBooleanConverter"/>
            <local:NotNullToVisibilityConverter x:Key="NotNullToVisibilityConverter"/>
            <local:NotNullToBooleanConverter x:Key="NotNullToBooleanConverter"/>
            <local:EnumToBooleanConverter x:Key="EnumToBooleanConverter"/>
            <local:BooleanToStringConverter x:Key="BooleanToStringConverter"/>
            <local:BooleanToColorConverter x:Key="BooleanToColorConverter"/>
        </ResourceDictionary>
    </Application.Resources>
</Application>
