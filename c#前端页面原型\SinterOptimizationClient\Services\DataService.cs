using Newtonsoft.Json;
using SinterOptimizationClient.Models;
using System;
using System.Collections.Generic;
using System.IO;
using System.Text;
using System.Threading.Tasks;

namespace SinterOptimizationClient.Services
{
    public class DataService : IDataService
    {
        private readonly string _dataDirectory;
        private readonly string _materialsFilePath;

        public DataService()
        {
            _dataDirectory = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData), "SinterOptimization");
            _materialsFilePath = Path.Combine(_dataDirectory, "materials.json");
            
            // 确保数据目录存在
            Directory.CreateDirectory(_dataDirectory);
        }

        public async Task<List<MaterialData>> LoadMaterialsAsync()
        {
            try
            {
                if (File.Exists(_materialsFilePath))
                {
                    var json = await File.ReadAllTextAsync(_materialsFilePath, Encoding.UTF8);
                    var materials = JsonConvert.DeserializeObject<List<MaterialData>>(json);
                    return materials ?? MaterialData.CreateDefaultMaterials();
                }
                else
                {
                    // 如果文件不存在，返回默认数据并保存
                    var defaultMaterials = MaterialData.CreateDefaultMaterials();
                    await SaveMaterialsAsync(defaultMaterials);
                    return defaultMaterials;
                }
            }
            catch (Exception)
            {
                // 如果加载失败，返回默认数据
                return MaterialData.CreateDefaultMaterials();
            }
        }

        public async Task SaveMaterialsAsync(List<MaterialData> materials)
        {
            try
            {
                var json = JsonConvert.SerializeObject(materials, Formatting.Indented);
                await File.WriteAllTextAsync(_materialsFilePath, json, Encoding.UTF8);
            }
            catch (Exception ex)
            {
                throw new InvalidOperationException($"保存原料数据失败: {ex.Message}", ex);
            }
        }

        public async Task<List<MaterialData>> ImportMaterialsFromFileAsync(string filePath)
        {
            try
            {
                var extension = Path.GetExtension(filePath).ToLower();
                
                switch (extension)
                {
                    case ".json":
                        return await ImportFromJsonAsync(filePath);
                    case ".csv":
                        return await ImportFromCsvAsync(filePath);
                    default:
                        throw new NotSupportedException($"不支持的文件格式: {extension}");
                }
            }
            catch (Exception ex)
            {
                throw new InvalidOperationException($"导入文件失败: {ex.Message}", ex);
            }
        }

        public async Task ExportMaterialsToFileAsync(List<MaterialData> materials, string filePath)
        {
            try
            {
                var extension = Path.GetExtension(filePath).ToLower();
                
                switch (extension)
                {
                    case ".json":
                        await ExportToJsonAsync(materials, filePath);
                        break;
                    case ".csv":
                        await ExportToCsvAsync(materials, filePath);
                        break;
                    default:
                        throw new NotSupportedException($"不支持的文件格式: {extension}");
                }
            }
            catch (Exception ex)
            {
                throw new InvalidOperationException($"导出文件失败: {ex.Message}", ex);
            }
        }

        private async Task<List<MaterialData>> ImportFromJsonAsync(string filePath)
        {
            var json = await File.ReadAllTextAsync(filePath, Encoding.UTF8);
            var materials = JsonConvert.DeserializeObject<List<MaterialData>>(json);
            return materials ?? new List<MaterialData>();
        }

        private async Task<List<MaterialData>> ImportFromCsvAsync(string filePath)
        {
            var materials = new List<MaterialData>();
            var lines = await File.ReadAllLinesAsync(filePath, Encoding.UTF8);
            
            // 跳过标题行
            for (int i = 1; i < lines.Length; i++)
            {
                var values = lines[i].Split(',');
                if (values.Length >= 10)
                {
                    var material = new MaterialData
                    {
                        Name = values[0],
                        Tfe = double.TryParse(values[1], out var tfe) ? tfe : 0,
                        Cao = double.TryParse(values[2], out var cao) ? cao : 0,
                        Sio2 = double.TryParse(values[3], out var sio2) ? sio2 : 0,
                        Mgo = double.TryParse(values[4], out var mgo) ? mgo : 0,
                        Al2o3 = double.TryParse(values[5], out var al2o3) ? al2o3 : 0,
                        Tio2 = double.TryParse(values[7], out var tio2) ? tio2 : 0,
                        Ig = double.TryParse(values[8], out var ig) ? ig : 0,
                        H2o = double.TryParse(values[9], out var h2o) ? h2o : 0,
                        PlannedPrice = values.Length > 19 && double.TryParse(values[19], out var price) ? price : 0,
                        MinRatio = values.Length > 21 && double.TryParse(values[21], out var minRatio) ? minRatio : 0,
                        MaxRatio = values.Length > 22 && double.TryParse(values[22], out var maxRatio) ? maxRatio : 100
                    };
                    materials.Add(material);
                }
            }
            
            return materials;
        }

        private async Task ExportToJsonAsync(List<MaterialData> materials, string filePath)
        {
            var json = JsonConvert.SerializeObject(materials, Formatting.Indented);
            await File.WriteAllTextAsync(filePath, json, Encoding.UTF8);
        }

        private async Task ExportToCsvAsync(List<MaterialData> materials, string filePath)
        {
            var csv = new StringBuilder();
            
            // 添加表头
            csv.AppendLine("品名,TFe,CaO,SiO2,MgO,Al2O3,C/S,TiO2,Ig,H2O,湿配比,干配比,烧成量,TFe,CaO,SiO2,MgO,Al2O3,吨矿单耗,计划单价,单位成本,最小配比,最大配比");
            
            // 添加数据行
            foreach (var material in materials)
            {
                csv.AppendLine($"{material.Name},{material.Tfe},{material.Cao},{material.Sio2},{material.Mgo},{material.Al2o3},{material.CS:F2},{material.Tio2},{material.Ig},{material.H2o},{material.WetRatio},{material.DryRatio},{material.BurnAmount},{material.BurnTfe},{material.BurnCao},{material.BurnSio2},{material.BurnMgo},{material.BurnAl2o3},{material.TonConsumption},{material.PlannedPrice},{material.UnitCost},{material.MinRatio},{material.MaxRatio}");
            }
            
            await File.WriteAllTextAsync(filePath, csv.ToString(), Encoding.UTF8);
        }
    }
}
