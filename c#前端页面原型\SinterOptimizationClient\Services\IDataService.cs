using SinterOptimizationClient.Models;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace SinterOptimizationClient.Services
{
    public interface IDataService
    {
        Task<List<MaterialData>> LoadMaterialsAsync();
        Task SaveMaterialsAsync(List<MaterialData> materials);
        Task<List<MaterialData>> ImportMaterialsFromFileAsync(string filePath);
        Task ExportMaterialsToFileAsync(List<MaterialData> materials, string filePath);
    }
}
