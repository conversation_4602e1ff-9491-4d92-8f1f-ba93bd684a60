using Newtonsoft.Json;
using SinterOptimizationClient.Models;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net.Http;
using System.Text;
using System.Threading.Tasks;

namespace SinterOptimizationClient.Services
{
    public class OptimizationService : IOptimizationService
    {
        private readonly HttpClient _httpClient;

        public OptimizationService(HttpClient httpClient)
        {
            _httpClient = httpClient;
        }

        public async Task<OptimizationResult> OptimizeAsync(OptimizationRequest request)
        {
            try
            {
                // 构建请求数据
                var requestData = new
                {
                    raw_materials = request.RawMaterials.Select(m => new
                    {
                        name = m.Name,
                        tfe = m.Tfe,
                        cao = m.Cao,
                        sio2 = m.Sio2,
                        mgo = m.Mgo,
                        al2o3 = m.Al2o3,
                        h2o = m.H2o,
                        ig = m.Ig,
                        price = m.Price,
                        min_ratio = m.MinRatio,
                        max_ratio = m.MaxRatio
                    }).ToList(),
                    target = new
                    {
                        tfe_target = request.Target.TfeTarget,
                        ro_target = request.Target.RoTarget,
                        mgo_target = request.Target.MgoTarget,
                        al2o3_target = request.Target.Al2o3Target
                    },
                    constraints = new
                    {
                        tfe_range = new[] { request.Constraints.TfeRange.Min, request.Constraints.TfeRange.Max },
                        ro_range = new[] { request.Constraints.RoRange.Min, request.Constraints.RoRange.Max },
                        mgo_range = new[] { request.Constraints.MgoRange.Min, request.Constraints.MgoRange.Max },
                        al2o3_range = new[] { request.Constraints.Al2o3Range.Min, request.Constraints.Al2o3Range.Max },
                        cost_range = new[] { request.Constraints.CostRange.Min, request.Constraints.CostRange.Max }
                    },
                    optimize_type = request.OptimizeType,
                    multi_solution = request.MultiSolution
                };

                var json = JsonConvert.SerializeObject(requestData);
                var content = new StringContent(json, Encoding.UTF8, "application/json");

                var response = await _httpClient.PostAsync("api/solve", content);
                var responseContent = await response.Content.ReadAsStringAsync();

                if (response.IsSuccessStatusCode)
                {
                    var responseData = JsonConvert.DeserializeObject<dynamic>(responseContent);
                    
                    if (responseData?.success == true)
                    {
                        return ParseSuccessResponse(responseData);
                    }
                    else
                    {
                        return new OptimizationResult
                        {
                            Success = false,
                            ErrorMessage = responseData?.error?.ToString() ?? "优化计算失败"
                        };
                    }
                }
                else
                {
                    return new OptimizationResult
                    {
                        Success = false,
                        ErrorMessage = $"服务器错误: {response.StatusCode} - {responseContent}"
                    };
                }
            }
            catch (HttpRequestException ex)
            {
                return new OptimizationResult
                {
                    Success = false,
                    ErrorMessage = $"网络连接错误: {ex.Message}"
                };
            }
            catch (TaskCanceledException ex)
            {
                return new OptimizationResult
                {
                    Success = false,
                    ErrorMessage = $"请求超时: {ex.Message}"
                };
            }
            catch (Exception ex)
            {
                return new OptimizationResult
                {
                    Success = false,
                    ErrorMessage = $"未知错误: {ex.Message}"
                };
            }
        }

        public async Task<bool> CheckServiceHealthAsync()
        {
            try
            {
                var response = await _httpClient.GetAsync("health");
                return response.IsSuccessStatusCode;
            }
            catch
            {
                return false;
            }
        }

        public async Task<string> GetServiceVersionAsync()
        {
            try
            {
                var response = await _httpClient.GetAsync("/");
                if (response.IsSuccessStatusCode)
                {
                    var content = await response.Content.ReadAsStringAsync();
                    var data = JsonConvert.DeserializeObject<dynamic>(content);
                    return data?.version?.ToString() ?? "未知版本";
                }
                return "无法获取版本信息";
            }
            catch
            {
                return "服务不可用";
            }
        }

        private OptimizationResult ParseSuccessResponse(dynamic responseData)
        {
            var result = new OptimizationResult
            {
                Success = true,
                OptimizationType = responseData.optimization_type?.ToString() ?? "",
                UnitCost = (double)(responseData.unit_cost ?? 0),
                ObjectiveValue = (double)(responseData.objective_value ?? 0),
                Iterations = (int)(responseData.iterations ?? 0),
                Message = responseData.message?.ToString() ?? "",
                Timestamp = DateTime.Now
            };

            // 解析最优配比
            if (responseData.optimal_ratios != null)
            {
                result.OptimalRatios = JsonConvert.DeserializeObject<Dictionary<string, double>>(
                    responseData.optimal_ratios.ToString());
            }

            // 解析烧结矿性质
            if (responseData.sinter_properties != null)
            {
                var props = responseData.sinter_properties;
                result.SinterProperties = new SinterProperties
                {
                    TFe = (double)(props.tfe ?? 0),
                    R = (double)(props.r ?? 0),
                    MgO = (double)(props.mgo ?? 0),
                    Al2O3 = (double)(props.al2o3 ?? 0),
                    CaO = (double)(props.cao ?? 0),
                    SiO2 = (double)(props.sio2 ?? 0),
                    TiO2 = (double)(props.tio2 ?? 0)
                };
            }

            // 解析备选方案
            if (responseData.alternative_solution != null)
            {
                var altSolution = responseData.alternative_solution;
                result.AlternativeSolution = new OptimizationResult
                {
                    Success = true,
                    OptimizationType = altSolution.optimization_type?.ToString() ?? "",
                    UnitCost = (double)(altSolution.unit_cost ?? 0),
                    ObjectiveValue = (double)(altSolution.objective_value ?? 0),
                    OptimalRatios = JsonConvert.DeserializeObject<Dictionary<string, double>>(
                        altSolution.optimal_ratios?.ToString() ?? "{}"),
                    SinterProperties = new SinterProperties
                    {
                        TFe = (double)(altSolution.sinter_properties?.tfe ?? 0),
                        R = (double)(altSolution.sinter_properties?.r ?? 0),
                        MgO = (double)(altSolution.sinter_properties?.mgo ?? 0),
                        Al2O3 = (double)(altSolution.sinter_properties?.al2o3 ?? 0),
                        CaO = (double)(altSolution.sinter_properties?.cao ?? 0),
                        SiO2 = (double)(altSolution.sinter_properties?.sio2 ?? 0),
                        TiO2 = (double)(altSolution.sinter_properties?.tio2 ?? 0)
                    }
                };
            }

            return result;
        }
    }
}
