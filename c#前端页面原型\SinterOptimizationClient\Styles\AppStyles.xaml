<ResourceDictionary xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml">

    <!-- 颜色定义 - 增强工业风配色方案 -->
    <SolidColorBrush x:Key="PrimaryBrush" Color="#1E3A8A"/>
    <SolidColorBrush x:Key="PrimaryDarkBrush" Color="#1E293B"/>
    <SolidColorBrush x:Key="LightGrayBrush" Color="#F3F4F6"/>
    <SolidColorBrush x:Key="OrangeBrush" Color="#F97316"/>
    <SolidColorBrush x:Key="OrangeDarkBrush" Color="#EA580C"/>
    <SolidColorBrush x:Key="GreenBrush" Color="#10B981"/>
    <SolidColorBrush x:Key="GreenDarkBrush" Color="#059669"/>
    <SolidColorBrush x:Key="DarkGrayBrush" Color="#1F2937"/>
    <SolidColorBrush x:Key="BorderBrush" Color="#E5E7EB"/>
    <SolidColorBrush x:Key="AlternateBrush" Color="#F8FAFC"/>
    <SolidColorBrush x:Key="SuccessBrush" Color="#10B981"/>
    <SolidColorBrush x:Key="WarningBrush" Color="#F59E0B"/>
    <SolidColorBrush x:Key="ErrorBrush" Color="#EF4444"/>

    <!-- 导航栏样式 -->
    <Style x:Key="NavigationBarStyle" TargetType="Border">
        <Setter Property="Background" Value="{StaticResource PrimaryBrush}"/>
        <Setter Property="Height" Value="60"/>
        <Setter Property="Padding" Value="20,0"/>
    </Style>

    <!-- 导航按钮样式 -->
    <Style x:Key="NavigationButtonStyle" TargetType="Button">
        <Setter Property="Width" Value="120"/>
        <Setter Property="Height" Value="40"/>
        <Setter Property="Margin" Value="5,0"/>
        <Setter Property="FontSize" Value="14"/>
        <Setter Property="FontWeight" Value="Medium"/>
        <Setter Property="Foreground" Value="White"/>
        <Setter Property="Background" Value="{StaticResource PrimaryBrush}"/>
        <Setter Property="BorderThickness" Value="0"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="Button">
                    <Border Background="{TemplateBinding Background}" 
                            CornerRadius="3"
                            Padding="{TemplateBinding Padding}">
                        <ContentPresenter HorizontalAlignment="Center" 
                                        VerticalAlignment="Center"/>
                    </Border>
                    <ControlTemplate.Triggers>
                        <Trigger Property="IsMouseOver" Value="True">
                            <Setter Property="Background" Value="#1E293B"/>
                        </Trigger>
                        <DataTrigger Binding="{Binding IsSelected}" Value="True">
                            <Setter Property="Background" Value="{StaticResource OrangeBrush}"/>
                        </DataTrigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <!-- 标题样式 -->
    <Style x:Key="SectionTitleStyle" TargetType="TextBlock">
        <Setter Property="FontSize" Value="16"/>
        <Setter Property="FontWeight" Value="Bold"/>
        <Setter Property="Foreground" Value="{StaticResource PrimaryBrush}"/>
        <Setter Property="Margin" Value="0,0,0,10"/>
    </Style>

    <!-- 输入框样式 - 增强视觉效果 -->
    <Style x:Key="InputTextBoxStyle" TargetType="TextBox">
        <Setter Property="Height" Value="32"/>
        <Setter Property="Padding" Value="8,6"/>
        <Setter Property="BorderThickness" Value="1"/>
        <Setter Property="BorderBrush" Value="{StaticResource BorderBrush}"/>
        <Setter Property="Background" Value="White"/>
        <Setter Property="FontSize" Value="13"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="TextBox">
                    <Border x:Name="border"
                            Background="{TemplateBinding Background}"
                            BorderBrush="{TemplateBinding BorderBrush}"
                            BorderThickness="{TemplateBinding BorderThickness}"
                            CornerRadius="4">
                        <ScrollViewer x:Name="PART_ContentHost"
                                    Padding="{TemplateBinding Padding}"/>
                        <Border.Effect>
                            <DropShadowEffect Color="Black"
                                            Opacity="0.05"
                                            ShadowDepth="1"
                                            BlurRadius="3"/>
                        </Border.Effect>
                    </Border>
                    <ControlTemplate.Triggers>
                        <Trigger Property="IsFocused" Value="True">
                            <Setter TargetName="border" Property="BorderBrush" Value="{StaticResource OrangeBrush}"/>
                            <Setter TargetName="border" Property="BorderThickness" Value="2"/>
                            <Setter Property="Effect">
                                <Setter.Value>
                                    <DropShadowEffect Color="#F97316"
                                                    Opacity="0.2"
                                                    ShadowDepth="0"
                                                    BlurRadius="6"/>
                                </Setter.Value>
                            </Setter>
                        </Trigger>
                        <Trigger Property="IsMouseOver" Value="True">
                            <Setter TargetName="border" Property="BorderBrush" Value="#D1D5DB"/>
                        </Trigger>
                        <Trigger Property="IsEnabled" Value="False">
                            <Setter TargetName="border" Property="Background" Value="#F9FAFB"/>
                            <Setter Property="Foreground" Value="#9CA3AF"/>
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <!-- 主要按钮样式 - 增强视觉效果 -->
    <Style x:Key="PrimaryButtonStyle" TargetType="Button">
        <Setter Property="Height" Value="38"/>
        <Setter Property="Padding" Value="18,0"/>
        <Setter Property="FontSize" Value="14"/>
        <Setter Property="FontWeight" Value="Medium"/>
        <Setter Property="Foreground" Value="White"/>
        <Setter Property="Background" Value="{StaticResource OrangeBrush}"/>
        <Setter Property="BorderThickness" Value="0"/>
        <Setter Property="Cursor" Value="Hand"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="Button">
                    <Border x:Name="border"
                            Background="{TemplateBinding Background}"
                            CornerRadius="5"
                            Padding="{TemplateBinding Padding}">
                        <ContentPresenter HorizontalAlignment="Center"
                                        VerticalAlignment="Center"/>
                        <Border.Effect>
                            <DropShadowEffect Color="Black"
                                            Opacity="0.15"
                                            ShadowDepth="2"
                                            BlurRadius="6"/>
                        </Border.Effect>
                    </Border>
                    <ControlTemplate.Triggers>
                        <Trigger Property="IsMouseOver" Value="True">
                            <Setter TargetName="border" Property="Background" Value="{StaticResource OrangeDarkBrush}"/>
                            <Setter Property="Effect">
                                <Setter.Value>
                                    <DropShadowEffect Color="Black"
                                                    Opacity="0.25"
                                                    ShadowDepth="4"
                                                    BlurRadius="8"/>
                                </Setter.Value>
                            </Setter>
                        </Trigger>
                        <Trigger Property="IsPressed" Value="True">
                            <Setter TargetName="border" Property="Background" Value="#DC2626"/>
                            <Setter Property="Effect">
                                <Setter.Value>
                                    <DropShadowEffect Color="Black"
                                                    Opacity="0.1"
                                                    ShadowDepth="1"
                                                    BlurRadius="3"/>
                                </Setter.Value>
                            </Setter>
                        </Trigger>
                        <Trigger Property="IsEnabled" Value="False">
                            <Setter TargetName="border" Property="Background" Value="#9CA3AF"/>
                            <Setter Property="Foreground" Value="#6B7280"/>
                            <Setter Property="Effect" Value="{x:Null}"/>
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <!-- 成功按钮样式 -->
    <Style x:Key="SuccessButtonStyle" TargetType="Button" BasedOn="{StaticResource PrimaryButtonStyle}">
        <Setter Property="Background" Value="{StaticResource GreenBrush}"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="Button">
                    <Border x:Name="border"
                            Background="{TemplateBinding Background}"
                            CornerRadius="5"
                            Padding="{TemplateBinding Padding}">
                        <ContentPresenter HorizontalAlignment="Center"
                                        VerticalAlignment="Center"/>
                        <Border.Effect>
                            <DropShadowEffect Color="Black"
                                            Opacity="0.15"
                                            ShadowDepth="2"
                                            BlurRadius="6"/>
                        </Border.Effect>
                    </Border>
                    <ControlTemplate.Triggers>
                        <Trigger Property="IsMouseOver" Value="True">
                            <Setter TargetName="border" Property="Background" Value="{StaticResource GreenDarkBrush}"/>
                        </Trigger>
                        <Trigger Property="IsPressed" Value="True">
                            <Setter TargetName="border" Property="Background" Value="#047857"/>
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <!-- 次要按钮样式 -->
    <Style x:Key="SecondaryButtonStyle" TargetType="Button" BasedOn="{StaticResource PrimaryButtonStyle}">
        <Setter Property="Background" Value="{StaticResource PrimaryBrush}"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="Button">
                    <Border x:Name="border"
                            Background="{TemplateBinding Background}"
                            CornerRadius="5"
                            Padding="{TemplateBinding Padding}">
                        <ContentPresenter HorizontalAlignment="Center"
                                        VerticalAlignment="Center"/>
                        <Border.Effect>
                            <DropShadowEffect Color="Black"
                                            Opacity="0.15"
                                            ShadowDepth="2"
                                            BlurRadius="6"/>
                        </Border.Effect>
                    </Border>
                    <ControlTemplate.Triggers>
                        <Trigger Property="IsMouseOver" Value="True">
                            <Setter TargetName="border" Property="Background" Value="{StaticResource PrimaryDarkBrush}"/>
                        </Trigger>
                        <Trigger Property="IsPressed" Value="True">
                            <Setter TargetName="border" Property="Background" Value="#0F172A"/>
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <!-- 表格样式 -->
    <Style x:Key="DataGridStyle" TargetType="DataGrid">
        <Setter Property="AutoGenerateColumns" Value="False"/>
        <Setter Property="CanUserAddRows" Value="False"/>
        <Setter Property="CanUserDeleteRows" Value="False"/>
        <Setter Property="CanUserReorderColumns" Value="False"/>
        <Setter Property="CanUserResizeRows" Value="False"/>
        <Setter Property="SelectionMode" Value="Single"/>
        <Setter Property="GridLinesVisibility" Value="All"/>
        <Setter Property="HorizontalGridLinesBrush" Value="{StaticResource BorderBrush}"/>
        <Setter Property="VerticalGridLinesBrush" Value="{StaticResource BorderBrush}"/>
        <Setter Property="RowHeight" Value="40"/>
        <Setter Property="HeadersVisibility" Value="Column"/>
        <Setter Property="AlternatingRowBackground" Value="{StaticResource AlternateBrush}"/>
    </Style>

    <!-- 表格标题样式 -->
    <Style x:Key="DataGridHeaderStyle" TargetType="DataGridColumnHeader">
        <Setter Property="Background" Value="{StaticResource PrimaryBrush}"/>
        <Setter Property="Foreground" Value="White"/>
        <Setter Property="FontWeight" Value="Bold"/>
        <Setter Property="HorizontalContentAlignment" Value="Center"/>
        <Setter Property="VerticalContentAlignment" Value="Center"/>
        <Setter Property="Padding" Value="8"/>
        <Setter Property="BorderThickness" Value="0,0,1,0"/>
        <Setter Property="BorderBrush" Value="{StaticResource BorderBrush}"/>
    </Style>

    <!-- 工业风卡片样式 -->
    <Style x:Key="IndustrialCardStyle" TargetType="Border">
        <Setter Property="Background" Value="White"/>
        <Setter Property="BorderBrush" Value="{StaticResource BorderBrush}"/>
        <Setter Property="BorderThickness" Value="1"/>
        <Setter Property="CornerRadius" Value="3"/>
        <Setter Property="Padding" Value="15"/>
        <Setter Property="Margin" Value="5"/>
        <Setter Property="Effect">
            <Setter.Value>
                <DropShadowEffect Color="Black" Opacity="0.1" ShadowDepth="2" BlurRadius="4"/>
            </Setter.Value>
        </Setter>
    </Style>

    <!-- 状态指示器样式 -->
    <Style x:Key="StatusIndicatorStyle" TargetType="Ellipse">
        <Setter Property="Width" Value="12"/>
        <Setter Property="Height" Value="12"/>
        <Setter Property="Margin" Value="0,0,5,0"/>
    </Style>

    <!-- 数值输入框样式 -->
    <Style x:Key="NumericInputStyle" TargetType="TextBox" BasedOn="{StaticResource InputTextBoxStyle}">
        <Setter Property="HorizontalContentAlignment" Value="Right"/>
        <Setter Property="FontFamily" Value="Consolas, Monaco, monospace"/>
    </Style>

    <!-- 分组标题样式 -->
    <Style x:Key="GroupTitleStyle" TargetType="TextBlock">
        <Setter Property="FontSize" Value="14"/>
        <Setter Property="FontWeight" Value="Bold"/>
        <Setter Property="Foreground" Value="{StaticResource DarkGrayBrush}"/>
        <Setter Property="Margin" Value="0,10,0,5"/>
        <Setter Property="Padding" Value="0,5,0,5"/>
    </Style>

    <!-- 工具栏按钮样式 -->
    <Style x:Key="ToolbarButtonStyle" TargetType="Button" BasedOn="{StaticResource PrimaryButtonStyle}">
        <Setter Property="Height" Value="32"/>
        <Setter Property="Padding" Value="12,0"/>
        <Setter Property="FontSize" Value="12"/>
        <Setter Property="Margin" Value="0,0,8,0"/>
    </Style>

    <!-- 警告文本样式 -->
    <Style x:Key="WarningTextStyle" TargetType="TextBlock">
        <Setter Property="Foreground" Value="#F59E0B"/>
        <Setter Property="FontSize" Value="12"/>
        <Setter Property="FontStyle" Value="Italic"/>
    </Style>

    <!-- 成功文本样式 -->
    <Style x:Key="SuccessTextStyle" TargetType="TextBlock">
        <Setter Property="Foreground" Value="#10B981"/>
        <Setter Property="FontSize" Value="12"/>
        <Setter Property="FontWeight" Value="Medium"/>
    </Style>

    <!-- 错误文本样式 -->
    <Style x:Key="ErrorTextStyle" TargetType="TextBlock">
        <Setter Property="Foreground" Value="#EF4444"/>
        <Setter Property="FontSize" Value="12"/>
        <Setter Property="FontWeight" Value="Medium"/>
    </Style>

</ResourceDictionary>
