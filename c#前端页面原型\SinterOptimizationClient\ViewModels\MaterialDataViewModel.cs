using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using Microsoft.Win32;
using SinterOptimizationClient.Models;
using SinterOptimizationClient.Services;
using System;
using System.Collections.ObjectModel;
using System.IO;
using System.Text;
using System.Threading.Tasks;
using System.Windows;

namespace SinterOptimizationClient.ViewModels
{
    public partial class MaterialDataViewModel : ObservableObject
    {
        private readonly IDataService _dataService;
        private readonly IDialogService _dialogService;

        [ObservableProperty]
        private ObservableCollection<MaterialData> materials = new();

        [ObservableProperty]
        private MaterialData? selectedMaterial;

        [ObservableProperty]
        private bool isEditing = false;

        [ObservableProperty]
        private string statusMessage = "就绪";

        public MaterialDataViewModel(IDataService dataService, IDialogService dialogService)
        {
            _dataService = dataService;
            _dialogService = dialogService;
            
            LoadDefaultMaterials();
        }

        private void LoadDefaultMaterials()
        {
            try
            {
                var defaultMaterials = MaterialData.CreateDefaultMaterials();
                Materials.Clear();
                foreach (var material in defaultMaterials)
                {
                    Materials.Add(material);
                }
                StatusMessage = $"已加载 {Materials.Count} 种原料";
            }
            catch (Exception ex)
            {
                StatusMessage = $"加载原料数据失败: {ex.Message}";
            }
        }

        [RelayCommand]
        private void AddMaterial()
        {
            var newMaterial = new MaterialData
            {
                Name = "新原料",
                MinRatio = 0,
                MaxRatio = 100
            };
            
            Materials.Add(newMaterial);
            SelectedMaterial = newMaterial;
            IsEditing = true;
            StatusMessage = "已添加新原料，请编辑相关参数";
        }

        [RelayCommand]
        private void EditMaterial()
        {
            if (SelectedMaterial != null)
            {
                IsEditing = true;
                StatusMessage = "编辑模式已启用";
            }
        }

        [RelayCommand]
        private void SaveMaterial()
        {
            if (SelectedMaterial != null)
            {
                if (SelectedMaterial.IsValid())
                {
                    IsEditing = false;
                    StatusMessage = "原料数据已保存";
                }
                else
                {
                    _dialogService.ShowError("数据验证失败", "请检查输入的数据是否正确");
                }
            }
        }

        [RelayCommand]
        private void CancelEdit()
        {
            IsEditing = false;
            StatusMessage = "已取消编辑";
        }

        [RelayCommand]
        private void DeleteMaterial()
        {
            if (SelectedMaterial != null)
            {
                var result = _dialogService.ShowConfirmation(
                    "确认删除", 
                    $"确定要删除原料 '{SelectedMaterial.Name}' 吗？");
                
                if (result)
                {
                    Materials.Remove(SelectedMaterial);
                    SelectedMaterial = null;
                    StatusMessage = "原料已删除";
                }
            }
        }

        [RelayCommand]
        private async Task ImportMaterials()
        {
            try
            {
                var openFileDialog = new OpenFileDialog
                {
                    Title = "导入原料数据",
                    Filter = "CSV文件 (*.csv)|*.csv|Excel文件 (*.xlsx)|*.xlsx|所有文件 (*.*)|*.*",
                    DefaultExt = "csv"
                };

                if (openFileDialog.ShowDialog() == true)
                {
                    StatusMessage = "正在导入数据...";
                    
                    // 这里应该实现实际的文件导入逻辑
                    await Task.Delay(1000); // 模拟导入过程
                    
                    StatusMessage = "数据导入完成";
                    _dialogService.ShowInformation("导入成功", "原料数据已成功导入");
                }
            }
            catch (Exception ex)
            {
                StatusMessage = "导入失败";
                _dialogService.ShowError("导入错误", $"导入数据时发生错误: {ex.Message}");
            }
        }

        [RelayCommand]
        private async Task ExportMaterials()
        {
            try
            {
                var saveFileDialog = new SaveFileDialog
                {
                    Title = "导出原料数据",
                    Filter = "CSV文件 (*.csv)|*.csv|Excel文件 (*.xlsx)|*.xlsx",
                    DefaultExt = "csv",
                    FileName = $"原料数据_{DateTime.Now:yyyyMMdd_HHmmss}"
                };

                if (saveFileDialog.ShowDialog() == true)
                {
                    StatusMessage = "正在导出数据...";
                    
                    await ExportToCsv(saveFileDialog.FileName);
                    
                    StatusMessage = "数据导出完成";
                    _dialogService.ShowInformation("导出成功", $"数据已导出到: {saveFileDialog.FileName}");
                }
            }
            catch (Exception ex)
            {
                StatusMessage = "导出失败";
                _dialogService.ShowError("导出错误", $"导出数据时发生错误: {ex.Message}");
            }
        }

        private async Task ExportToCsv(string fileName)
        {
            var csv = new StringBuilder();
            
            // 添加表头
            csv.AppendLine("品名,TFe,CaO,SiO2,MgO,Al2O3,C/S,TiO2,Ig,H2O,湿配比,干配比,烧成量,TFe,CaO,SiO2,MgO,Al2O3,吨矿单耗,计划单价,单位成本,最小配比,最大配比");
            
            // 添加数据行
            foreach (var material in Materials)
            {
                csv.AppendLine($"{material.Name},{material.Tfe},{material.Cao},{material.Sio2},{material.Mgo},{material.Al2o3},{material.CS:F2},{material.Tio2},{material.Ig},{material.H2o},{material.WetRatio},{material.DryRatio},{material.BurnAmount},{material.BurnTfe},{material.BurnCao},{material.BurnSio2},{material.BurnMgo},{material.BurnAl2o3},{material.TonConsumption},{material.PlannedPrice},{material.UnitCost},{material.MinRatio},{material.MaxRatio}");
            }
            
            await File.WriteAllTextAsync(fileName, csv.ToString(), Encoding.UTF8);
        }

        [RelayCommand]
        private void RefreshData()
        {
            LoadDefaultMaterials();
        }

        // 计算汇总数据
        public void CalculateSummary()
        {
            // 这里实现汇总计算逻辑
            // 包括累计、烧结矿成分、计划目标等
        }
    }
}
