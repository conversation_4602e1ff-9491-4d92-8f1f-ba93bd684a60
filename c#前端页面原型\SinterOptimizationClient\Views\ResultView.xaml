<UserControl x:Class="SinterOptimizationClient.Views.ResultView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             mc:Ignorable="d" 
             d:DesignHeight="900" d:DesignWidth="1600">

    <Grid Background="{StaticResource LightGrayBrush}">
        <Grid.ColumnDefinitions>
            <!-- 区域1：优化结果表格 - 占60% -->
            <ColumnDefinition Width="0.6*"/>
            <!-- 区域2：算法信息与辅助数据 - 占40% -->
            <ColumnDefinition Width="0.4*"/>
        </Grid.ColumnDefinitions>

        <!-- 区域1：优化结果表格 -->
        <Border Grid.Column="0" 
                Background="White" 
                BorderBrush="{StaticResource BorderBrush}" 
                BorderThickness="0,0,1,0"
                Padding="20">
            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="*"/>
                    <RowDefinition Height="Auto"/>
                </Grid.RowDefinitions>

                <!-- 标题和当前优化类型指示器 -->
                <StackPanel Grid.Row="0" Margin="0,0,0,15">
                    <TextBlock Text="优化计算结果"
                               FontSize="18"
                               FontWeight="Bold"
                               Foreground="{StaticResource PrimaryBrush}"
                               Margin="0,0,0,10"/>

                    <!-- 当前显示的优化类型指示器 -->
                    <Border Background="{Binding IsCostOptimalSelected, Converter={StaticResource BooleanToColorConverter}, ConverterParameter=#F97316|#10B981}"
                            CornerRadius="15"
                            Padding="10,5"
                            HorizontalAlignment="Left">
                        <StackPanel Orientation="Horizontal">
                            <TextBlock Text="{Binding IsCostOptimalSelected, Converter={StaticResource BooleanToStringConverter}, ConverterParameter=💰|🎯}"
                                       FontSize="14"
                                       Margin="0,0,5,0"/>
                            <TextBlock Text="{Binding IsCostOptimalSelected, Converter={StaticResource BooleanToStringConverter}, ConverterParameter=成本最优结果|质量最优结果}"
                                       FontSize="14"
                                       FontWeight="Bold"
                                       Foreground="White"/>
                        </StackPanel>
                    </Border>
                </StackPanel>

                <!-- 标签页切换 -->
                <StackPanel Grid.Row="1" Orientation="Horizontal" Margin="0,0,0,15">
                    <Button Width="150" Height="35"
                            Command="{Binding SwitchToCostOptimalCommand}"
                            Style="{StaticResource NavigationButtonStyle}">
                        <Button.Background>
                            <SolidColorBrush Color="{Binding IsCostOptimalSelected, Converter={StaticResource BooleanToColorConverter}, ConverterParameter=#F97316|#6B7280}"/>
                        </Button.Background>
                        <StackPanel Orientation="Horizontal">
                            <TextBlock Text="💰" FontSize="12" Margin="0,0,5,0"/>
                            <TextBlock Text="成本最优结果" FontSize="12" Foreground="White"/>
                        </StackPanel>
                    </Button>
                    <Button Width="150" Height="35"
                            Command="{Binding SwitchToQualityOptimalCommand}"
                            Style="{StaticResource NavigationButtonStyle}"
                            Margin="5,0,0,0">
                        <Button.Background>
                            <SolidColorBrush Color="{Binding IsQualityOptimalSelected, Converter={StaticResource BooleanToColorConverter}, ConverterParameter=#10B981|#6B7280}"/>
                        </Button.Background>
                        <StackPanel Orientation="Horizontal">
                            <TextBlock Text="🎯" FontSize="12" Margin="0,0,5,0"/>
                            <TextBlock Text="质量最优结果" FontSize="12" Foreground="White"/>
                        </StackPanel>
                    </Button>
                </StackPanel>

                <!-- 结果表格 -->
                <DataGrid Grid.Row="2"
                          ItemsSource="{Binding CurrentSolutions}"
                          SelectedItem="{Binding SelectedSolution}"
                          Style="{StaticResource DataGridStyle}"
                          ColumnHeaderStyle="{StaticResource DataGridHeaderStyle}">
                    
                    <!-- 成本最优结果表格列 -->
                    <DataGrid.Columns>
                        <DataGridTextColumn Header="方案" Binding="{Binding SolutionId}" Width="80"/>
                        <DataGridTextColumn Header="湿配比(%)" Binding="{Binding FormattedWetRatios}" Width="250">
                            <DataGridTextColumn.ElementStyle>
                                <Style TargetType="TextBlock">
                                    <Setter Property="TextWrapping" Value="Wrap"/>
                                    <Setter Property="VerticalAlignment" Value="Center"/>
                                </Style>
                            </DataGridTextColumn.ElementStyle>
                        </DataGridTextColumn>
                        <DataGridTextColumn Header="成本(元/吨)" Binding="{Binding Cost, StringFormat=F1}" Width="120"/>
                        <DataGridTextColumn Header="TFe(%)" Binding="{Binding TFe, StringFormat=F1}" Width="100"/>
                        <DataGridTextColumn Header="R" Binding="{Binding R, StringFormat=F2}" Width="100"/>
                        <DataGridTemplateColumn Header="操作" Width="100">
                            <DataGridTemplateColumn.CellTemplate>
                                <DataTemplate>
                                    <Button Content="查看详情" 
                                            Style="{StaticResource PrimaryButtonStyle}"
                                            Command="{Binding DataContext.ViewSolutionDetailsCommand, RelativeSource={RelativeSource AncestorType=UserControl}}"
                                            CommandParameter="{Binding}"
                                            Height="25"
                                            FontSize="11"/>
                                </DataTemplate>
                            </DataGridTemplateColumn.CellTemplate>
                        </DataGridTemplateColumn>
                    </DataGrid.Columns>
                </DataGrid>

                <!-- 状态栏 -->
                <Border Grid.Row="3" 
                        Background="{StaticResource PrimaryBrush}" 
                        Padding="10,5" 
                        Margin="0,10,0,0">
                    <TextBlock Text="{Binding StatusMessage}" 
                               Foreground="White" 
                               FontSize="12" 
                               VerticalAlignment="Center"/>
                </Border>
            </Grid>
        </Border>

        <!-- 区域2：可视化图表 -->
        <Border Grid.Column="1"
                Background="#F9FAFB"
                Padding="15">
            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="*"/>
                    <RowDefinition Height="*"/>
                </Grid.RowDefinitions>

                <!-- 标题和操作按钮 -->
                <StackPanel Grid.Row="0" Margin="0,0,0,10">
                    <TextBlock Text="优化结果可视化"
                               FontSize="16"
                               FontWeight="Bold"
                               Foreground="{StaticResource PrimaryBrush}"
                               Margin="0,0,0,10"/>

                    <StackPanel Orientation="Horizontal">
                        <Button Content="导出结果"
                                Background="{StaticResource PrimaryBrush}"
                                Foreground="White"
                                FontSize="11"
                                Height="26"
                                Width="80"
                                Command="{Binding ExportResultsCommand}"
                                Margin="0,0,8,0"/>

                        <Button Content="刷新"
                                Background="{StaticResource SecondaryBrush}"
                                Foreground="White"
                                FontSize="11"
                                Height="26"
                                Width="60"
                                Command="{Binding RefreshResultsCommand}"/>
                    </StackPanel>
                </StackPanel>

                <!-- 上半部分：成分对比图表 -->
                <Grid Grid.Row="1" Margin="0,0,0,8">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="*"/>
                    </Grid.ColumnDefinitions>

                    <!-- 成分达标情况 -->
                    <Border Grid.Column="0" Background="White" CornerRadius="6" Padding="10" Margin="0,0,4,0">
                        <StackPanel>
                            <TextBlock Text="成分达标情况" FontSize="12" FontWeight="Bold" Margin="0,0,0,8"/>
                            <Canvas Height="120" Background="#F8F9FA">
                                <!-- 这里将添加成分达标的可视化图表 -->
                                <Rectangle Fill="#10B981" Width="30" Height="80" Canvas.Left="20" Canvas.Top="30"/>
                                <TextBlock Text="TFe" FontSize="10" Canvas.Left="22" Canvas.Top="115"/>

                                <Rectangle Fill="#F59E0B" Width="30" Height="60" Canvas.Left="60" Canvas.Top="50"/>
                                <TextBlock Text="CaO" FontSize="10" Canvas.Left="62" Canvas.Top="115"/>

                                <Rectangle Fill="#10B981" Width="30" Height="90" Canvas.Left="100" Canvas.Top="20"/>
                                <TextBlock Text="SiO₂" FontSize="10" Canvas.Left="100" Canvas.Top="115"/>

                                <Rectangle Fill="#EF4444" Width="30" Height="40" Canvas.Left="140" Canvas.Top="70"/>
                                <TextBlock Text="MgO" FontSize="10" Canvas.Left="142" Canvas.Top="115"/>
                            </Canvas>
                        </StackPanel>
                    </Border>

                    <!-- 成本分析 -->
                    <Border Grid.Column="1" Background="White" CornerRadius="6" Padding="10" Margin="4,0,0,0">
                        <StackPanel>
                            <TextBlock Text="成本构成分析" FontSize="12" FontWeight="Bold" Margin="0,0,0,8"/>
                            <Canvas Height="120" Background="#F8F9FA">
                                <!-- 饼图样式的成本分析 -->
                                <Ellipse Fill="#3B82F6" Width="80" Height="80" Canvas.Left="40" Canvas.Top="20"/>
                                <Path Fill="#10B981" Data="M 80,60 A 40,40 0 0,1 120,60 L 80,60 Z" Canvas.Left="0" Canvas.Top="0"/>
                                <Path Fill="#F59E0B" Data="M 80,60 A 40,40 0 0,1 80,20 L 80,60 Z" Canvas.Left="0" Canvas.Top="0"/>
                                <TextBlock Text="总成本" FontSize="10" Canvas.Left="65" Canvas.Top="105"/>
                            </Canvas>
                        </StackPanel>
                    </Border>
                </Grid>

                <!-- 下半部分：配比分布和趋势 -->
                <Grid Grid.Row="2" Margin="0,8,0,0">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="*"/>
                    </Grid.ColumnDefinitions>

                    <!-- 配比分布 -->
                    <Border Grid.Column="0" Background="White" CornerRadius="6" Padding="10" Margin="0,0,4,0">
                        <StackPanel>
                            <TextBlock Text="湿配比分布" FontSize="12" FontWeight="Bold" Margin="0,0,0,8"/>
                            <Canvas Height="120" Background="#F8F9FA">
                                <!-- 配比分布条形图 -->
                                <Rectangle Fill="#6366F1" Width="100" Height="15" Canvas.Left="10" Canvas.Top="20"/>
                                <TextBlock Text="铁矿粉" FontSize="9" Canvas.Left="115" Canvas.Top="22"/>

                                <Rectangle Fill="#8B5CF6" Width="60" Height="15" Canvas.Left="10" Canvas.Top="40"/>
                                <TextBlock Text="烧结返矿" FontSize="9" Canvas.Left="75" Canvas.Top="42"/>

                                <Rectangle Fill="#06B6D4" Width="40" Height="15" Canvas.Left="10" Canvas.Top="60"/>
                                <TextBlock Text="石灰石" FontSize="9" Canvas.Left="55" Canvas.Top="62"/>

                                <Rectangle Fill="#10B981" Width="30" Height="15" Canvas.Left="10" Canvas.Top="80"/>
                                <TextBlock Text="其他" FontSize="9" Canvas.Left="45" Canvas.Top="82"/>
                            </Canvas>
                        </StackPanel>
                    </Border>

                    <!-- 质量指标 -->
                    <Border Grid.Column="1" Background="White" CornerRadius="6" Padding="10" Margin="4,0,0,0">
                        <StackPanel>
                            <TextBlock Text="质量指标评估" FontSize="12" FontWeight="Bold" Margin="0,0,0,8"/>
                            <Canvas Height="120" Background="#F8F9FA">
                                <!-- 雷达图样式的质量指标 -->
                                <Polygon Fill="#3B82F6" Opacity="0.3"
                                         Points="80,20 120,40 100,80 60,80 40,40"
                                         Stroke="#3B82F6" StrokeThickness="2"/>
                                <Ellipse Fill="#3B82F6" Width="4" Height="4" Canvas.Left="78" Canvas.Top="18"/>
                                <Ellipse Fill="#3B82F6" Width="4" Height="4" Canvas.Left="118" Canvas.Top="38"/>
                                <Ellipse Fill="#3B82F6" Width="4" Height="4" Canvas.Left="98" Canvas.Top="78"/>
                                <Ellipse Fill="#3B82F6" Width="4" Height="4" Canvas.Left="58" Canvas.Top="78"/>
                                <Ellipse Fill="#3B82F6" Width="4" Height="4" Canvas.Left="38" Canvas.Top="38"/>

                                <TextBlock Text="综合评分" FontSize="10" Canvas.Left="60" Canvas.Top="105"/>
                            </Canvas>
                        </StackPanel>
                    </Border>
                </Grid>
            </Grid>
        </Border>

    </Grid>
</UserControl>
