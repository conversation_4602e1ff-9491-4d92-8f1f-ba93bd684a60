#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
系统测试脚本 - 验证C#客户端和Python后端的完整功能
"""

import requests
import json
import time

def test_backend_health():
    """测试后端健康状态"""
    print("🔍 测试后端健康状态...")
    try:
        response = requests.get("http://localhost:5000/health", timeout=5)
        if response.status_code == 200:
            print("✅ 后端服务正常")
            return True
        else:
            print(f"❌ 后端服务异常: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 后端连接失败: {e}")
        return False

def test_optimization_api():
    """测试优化API"""
    print("\n🧪 测试优化API...")
    
    test_data = {
        "raw_materials": [
            {"name": "碱性精粉", "tfe": 63.76, "cao": 1.94, "sio2": 4.95, "mgo": 1.85, "al2o3": 0.60, "h2o": 8.20, "ig": 1.23, "price": 752.21, "min_ratio": 0, "max_ratio": 30},
            {"name": "酸性精粉", "tfe": 64.89, "cao": 0.70, "sio2": 6.32, "mgo": 0.92, "al2o3": 0.72, "h2o": 9.90, "ig": -0.05, "price": 752.21, "min_ratio": 0, "max_ratio": 30},
            {"name": "生石灰", "tfe": 0.00, "cao": 71.74, "sio2": 3.52, "mgo": 2.28, "al2o3": 1.19, "h2o": 7.00, "ig": 16.33, "price": 219.00, "min_ratio": 3, "max_ratio": 8},
            {"name": "焦粉", "tfe": 0.19, "cao": 0.37, "sio2": 8.82, "mgo": 0.22, "al2o3": 3.31, "h2o": 13.15, "ig": 79.40, "price": 520.00, "min_ratio": 3, "max_ratio": 6}
        ],
        "target": {"tfe_target": 55.0, "ro_target": 1.90, "mgo_target": 2.0, "al2o3_target": 1.5},
        "constraints": {
            "tfe_range": [54, 56],
            "ro_range": [1.8, 2.0],
            "mgo_range": [1.5, 2.5],
            "al2o3_range": [1.0, 2.0],
            "cost_range": [500, 800]
        },
        "optimize_type": "cost",
        "multi_solution": True
    }
    
    try:
        response = requests.post("http://localhost:5000/api/solve", json=test_data, timeout=30)
        if response.status_code == 200:
            result = response.json()
            if result.get('success'):
                print("✅ 优化计算成功!")
                print(f"   优化类型: {result.get('optimization_type')}")
                print(f"   单位成本: {result.get('unit_cost', 0):.2f} 元/吨")
                print(f"   迭代次数: {result.get('iterations', 0)}")
                
                # 显示最优配比
                ratios = result.get('optimal_ratios', {})
                print("   最优配比:")
                for material, ratio in ratios.items():
                    if ratio > 0.01:
                        print(f"     {material}: {ratio:.2f}%")
                
                return True
            else:
                print(f"❌ 优化失败: {result.get('error', '未知错误')}")
                return False
        else:
            print(f"❌ API请求失败: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ API测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("=" * 60)
    print("🔥 烧结矿成分及指标计算系统 - 系统测试")
    print("=" * 60)
    
    # 测试后端
    if not test_backend_health():
        print("\n❌ 后端服务测试失败")
        return False
    
    # 测试优化API
    if not test_optimization_api():
        print("\n❌ 优化API测试失败")
        return False
    
    print("\n" + "=" * 60)
    print("🎉 系统测试全部通过!")
    print("💡 C#客户端和Python后端都在正常运行")
    print("🖥️ 您现在可以在C#客户端中进行完整的操作测试")
    print("=" * 60)
    
    return True

if __name__ == "__main__":
    main()
