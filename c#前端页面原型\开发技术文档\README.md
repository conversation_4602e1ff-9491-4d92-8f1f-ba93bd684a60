# 烧结矿成分及指标计算系统

基于SQP二次序列规划算法的烧结配料优化系统，支持成本最优和质量最优双目标优化。

## 系统架构

```
烧结矿成分及指标计算系统
├── C# WPF 客户端 (前端)
│   ├── 导航栏模块
│   ├── 物料信息表模块  
│   ├── 优化计算界面模块
│   ├── 优化结果界面模块
│   └── HTTP通信服务
├── Python Flask 后端 (算法服务)
│   ├── SQP优化算法
│   ├── RESTful API接口
│   ├── 数据验证与处理
│   └── 结果格式化
└── 数据层
    ├── 原料数据管理
    ├── 配置参数存储
    └── 计算结果缓存
```

## 功能特性

### 🔥 核心功能
- **物料信息管理**: 支持原料数据的增删改查、导入导出
- **多目标优化**: 成本最优和质量最优双目标优化
- **约束条件设置**: 灵活的成分范围、配比约束设置
- **结果可视化**: 直观的结果展示和详细分析
- **数据导出**: 支持Excel/CSV格式结果导出

### 🎯 技术特点
- **SQP算法**: 采用Python实现的SQP二次序列规划算法
- **工业风UI**: 符合工业软件标准的用户界面设计
- **实时计算**: 高效的优化计算和结果反馈
- **数据验证**: 完善的输入数据验证和错误处理

## 环境要求

### 开发环境
- **Python**: 3.8+ 
- **.NET**: 6.0+
- **Visual Studio**: 2022 或 Visual Studio Code
- **操作系统**: Windows 10/11

### Python依赖
```
flask==2.3.3
flask-cors==4.0.0
numpy==1.24.3
scipy==1.11.1
```

### C#依赖
- Newtonsoft.Json
- Microsoft.Extensions.Http
- CommunityToolkit.Mvvm
- MaterialDesignThemes

## 快速启动

### 方式一：使用启动脚本（推荐）
```bash
# 双击运行启动脚本
start_system.bat
```

### 方式二：手动启动

#### 1. 启动Python后端
```bash
cd py
pip install -r requirements.txt
python optimization_service.py
```

#### 2. 启动C#客户端
```bash
cd SinterOptimizationClient
dotnet restore
dotnet build
dotnet run
```

#### 3. 测试系统集成
```bash
python test_integration.py
```

## 使用指南

### 1. 物料信息管理
- 在物料信息表中查看和编辑原料基础数据
- 支持添加、删除、修改原料信息
- 可导入/导出CSV格式的原料数据

### 2. 优化计算设置
- **原料数据库参数**: 补充原料的物理水、烧损率、单价等参数
- **优化目标选择**: 选择成本最优或质量最优
- **约束条件设置**: 设置成分范围、碱度约束、配比约束等

### 3. 结果查看分析
- 查看成本最优和质量最优的计算结果
- 分析最优配比和烧结矿成分
- 导出详细的计算结果报告

## API接口

### 健康检查
```
GET /health
```

### 优化计算
```
POST /api/solve
Content-Type: application/json

{
  "raw_materials": [...],
  "target": {...},
  "constraints": {...},
  "optimize_type": "cost|quality",
  "multi_solution": true
}
```

## 项目结构

```
├── SinterOptimizationClient/          # C# WPF客户端
│   ├── Models/                        # 数据模型
│   ├── ViewModels/                    # 视图模型
│   ├── Views/                         # 用户界面
│   ├── Services/                      # 服务层
│   ├── Converters/                    # 数据转换器
│   └── Styles/                        # 样式资源
├── py/                                # Python后端
│   ├── optimization_service.py        # Flask服务主文件
│   └── requirements.txt               # Python依赖
├── test_integration.py                # 集成测试脚本
├── start_system.bat                   # 系统启动脚本
└── README.md                          # 项目说明
```

## 开发说明

### 添加新原料
1. 在`MaterialData.CreateDefaultMaterials()`中添加原料数据
2. 更新Python后端的`DEFAULT_MATERIALS`列表
3. 重新启动系统

### 修改优化算法
1. 编辑`py/optimization_service.py`中的SQP算法实现
2. 调整目标函数和约束条件
3. 测试算法收敛性和计算精度

### 自定义UI样式
1. 修改`Styles/AppStyles.xaml`中的样式定义
2. 调整颜色、字体、布局等视觉元素
3. 确保符合工业软件设计规范

## 故障排除

### 常见问题

**Q: Python服务启动失败**
A: 检查Python环境和依赖包是否正确安装，确保端口5000未被占用

**Q: C#客户端无法连接后端**
A: 确认Python服务正在运行，检查防火墙设置，验证URL配置

**Q: 优化计算失败**
A: 检查输入参数是否合理，约束条件是否过于严格，原料数据是否完整

**Q: 界面显示异常**
A: 确认.NET 6.0环境正确安装，检查XAML资源文件是否完整

### 日志查看
- Python后端日志：控制台输出
- C#客户端日志：Visual Studio输出窗口
- 系统错误：Windows事件查看器

## 技术支持

如遇技术问题，请提供以下信息：
1. 操作系统版本
2. Python和.NET版本
3. 错误信息截图
4. 操作步骤描述

## 版本历史

### v1.0.0 (2024-01-30)
- 初始版本发布
- 实现基础的SQP优化算法
- 完成C# WPF客户端界面
- 支持成本和质量双目标优化

## 许可证

本项目采用MIT许可证，详见LICENSE文件。
