# 烧结矿成分及指标计算系统 - 前端需求文档

## 一、整体定位与目标

本系统为客户端桌面应用，用于实现烧结矿成分计算、配料优化及结果展示，支持用户通过可视化界面完成原料数据维护、参数输入、优化计算及结果查看全流程。页面采用**横向无间隙布局**，组件风格统一、美观，功能覆盖烧结矿成分计算全流程，确保数据展示与操作逻辑连贯。

## 二、整体布局要求



1.  **布局原则**：全页面横向排列，无纵向分割，各模块无缝衔接（间距≤2px），整体视觉连贯；

2.  **响应式适配**：支持客户端常用分辨率（1920×1080 及以上），内容随窗口缩放自适应，核心数据不溢出；

3.  **风格要求**：工业风与简洁风结合，主色调采用深蓝（#1E3A8A）+ 浅灰（#F3F4F6），辅助色用橙色（#F97316）突出操作区，组件边框圆角 3px，阴影弱化（0 2px 4px rgba (0,0,0,0.1)）。

## 三、页面结构总览

系统包含**1 个导航栏**+**2 个主页面**，通过导航栏切换：



*   导航栏：固定顶部，含系统名、logo、页面切换按钮；

*   主页面 1：优化计算界面（核心操作页）；

*   主页面 2：优化结果界面（数据展示页）。

## 四、详细模块设计

### （一）导航栏



1.  **位置**：页面顶部，高度 60px，宽度 100%，固定不动；

2.  **内容**：

*   左侧：系统 logo（200×60px，png 格式）+ 系统名称 “烧结矿成分及指标计算系统”（18px 粗体，白色文字，背景深蓝）；

*   右侧：2 个页面切换按钮（“优化计算”“优化结果”），按钮宽 120px，高 40px，选中状态为橙色背景 + 白色文字，未选中为深蓝背景 + 白色文字，点击切换对应主页面；

1.  **交互**：按钮 hover 时背景色加深（#1E293B），点击无跳转延迟。

### （二）主页面 1：优化计算界面

#### 1. 顶部：物料信息表（原料数据库基础表）



*   **位置**：导航栏下方，占页面高度 25%，宽度 100%；

*   **功能**：展示 / 维护原料基础信息（人工可编辑），支持数据增删改；

*   **列信息**（横向排列，每列宽自适应内容）：



| 品名    | TFe    | CaO    | SiO2   | MgO    | Al2O3     | C/S    | TiO2  | Ig         | H2O    | 湿配比    | 干配比    | 烧成量    | TFe    | CaO   | SiO2  | MgO    | Al2O3 | 吨矿单耗        | 计划单价     | 单位成本        |
| ----- | ------ | ------ | ------ | ------ | --------- | ------ | ----- | ---------- | ------ | ------ | ------ | ------ | ------ | ----- | ----- | ------ | ----- | ----------- | -------- | ----------- |
| 碱性精粉  | 63.76  | 1.94   | 4.95   | 1.85   | 0.60      | 0.23   | 0.01  | 1.23       | 8.20   | 0.0    | 0.00   | 0.00   | 0.00   | 0.00  | 0.00  | 0.00   | 0.00  | 0           | 752.21   | 0           |
| 酸性精粉  | 64.89  | 0.70   | 6.32   | 0.92   | 0.72      | 0.06   | 0.09  | -0.05      | 9.90   | 0.0    | 0.00   | 0.00   | 0.00   | 0.00  | 0.00  | 0.00   | 0.00  | 0           | 752.21   | 0           |
| 海瑞    | 58.07  | 0.10   | 6.21   | 0.28   | 2.52      | 0.01   | 0.07  | 9.07       | 6.00   | 0.0    | 0.00   | 0.00   | 0.00   | 0.00  | 0.00  | 0.00   | 0.00  | 0           | 822.98   | 0           |
| 印粉海娜  | 63.66  | 0.10   | 4.01   | 0.24   | 2.42      | 0.02   | 0.06  | 1.60       | 6.70   | 20.0   | 18.66  | 18.36  | 11.88  | 0.02  | 0.75  | 0.04   | 0.45  | 218.3873118 | 832.98   | 181.912263  |
| 巴西粗粉  | 64.64  | 0.20   | 4.69   | 0.11   | 0.73      | 0.03   | 0.02  | 1.33       | 6.70   | 0.0    | 0.00   | 0.00   | 0.00   | 0.00  | 0.00  | 0.00   | 0.00  | 0           | 1473.05  | 0           |
| 俄罗斯精粉 | 62.95  | 1.71   | 4.61   | 3.70   | 2.29      | 0.06   | 0.22  | -0.35      | 10.00  | 14.0   | 12.60  | 12.64  | 7.93   | 0.22  | 0.58  | 0.47   | 0.29  | 147.4641012 | 772.21   | 113.8732536 |
| 高炉返矿  | 55.54  | 10.60  | 5.59   | 2.34   | 2.09      | 0.55   | 0.00  | 1.73       | 0.50   | 20.0   | 19.90  | 19.56  | 11.05  | 2.11  | 1.11  | 0.47   | 0.42  | 232.8996519 | 550.00   | 128.0948086 |
| 回收料   | 56.16  | 6.56   | 6.31   | 2.39   | 2.51      | 1.59   | 0.20  | 1.74       | 10.73  | 8.0    | 7.14   | 7.02   | 4.01   | 0.47  | 0.45  | 0.17   | 0.18  | 83.5817163  | 100.00   | 8.35817163  |
| 钢渣    | 26.46  | 28.15  | 15.43  | 2.79   | 2.53      | 0.27   | 0.00  | 12.05      | 7.60   | 4.0    | 3.70   | 3.25   | 0.98   | 1.04  | 0.57  | 0.10   | 0.09  | 43.25613636 | 550.00   | 23.790875   |
| 氧化铁皮  | 69.73  | 0.50   | 1.50   | 0.00   | 2.88      | 0.01   | 0.00  | -1.52      | 5.90   | 0.0    | 0.00   | 0.00   | 0.00   | 0.00  | 0.00  | 0.00   | 0.00  | 0           | 750.00   | 0           |
| 生石灰   | 0.00   | 71.74  | 3.52   | 2.28   | 1.19      | 0.50   | 0.00  | 16.33      | 7.00   | 6.6    | 6.14   | 5.14   | 0.00   | 4.40  | 0.22  | 0.14   | 0.07  | 71.8360836  | 219.00   | 15.73210231 |
| 轻烧白云石 | 0.00   | 42.67  | 5.31   | 26.12  | 0.10      | 0.51   | 0.00  | 19.73      | 1.50   | 3.0    | 2.96   | 2.37   | 0.00   | 1.26  | 0.16  | 0.77   | 0.00  | 34.58384279 | 183.76   | 6.355126951 |
| 焦粉    | 0.19   | 0.37   | 8.82   | 0.22   | 3.31      | 79.40  | 0.00  | 79.40      | 13.15  | 5.0    | 4.34   | 0.89   | 0.01   | 0.02  | 0.38  | 0.01   | 0.14  | 50.82244917 | 520.00   | 26.42767357 |
| 澳粉纵横  | 60.80  | 0.10   | 4.35   | 0.20   | 2.30      | 0.01   | 0.06  | 6.89       | 8.30   | 20.0   | 18.34  | 17.08  | 11.15  | 0.02  | 0.80  | 0.04   | 0.42  | 214.6421918 | 832.98   | 178.7926529 |
| 空白    |        |        |        |        |           |        |       |            |        |        |        |        |        |       |       |        |       |             |          |             |
| 累计    | 燃料参考灰分 |        | 18.61  |        |           |        |       |            | 3.52   | 100.6  | 93.8   | 86.3   | 47.0   | 9.6   | 5.0   | 2.2    | 2.1   | 1097.473485 | 合计成本     | 683.3369276 |
| 烧结矿成分 | TFe=   | 53.92  |        | CaO=   | 11.07     |        | SiO2= | 5.81       |        | R=     | 1.90   |        | MgO=   | 2.56  |       | Al2O3= | 2.40  |             | 计划成本     | 680.44      |
| 计划目标  | TFe=   | 55.0   | ±1     |        | R=        | 1.90   | ±0.12 |            |        | TiO2=  | 0.23   |        | MgO=   | 2.82  |       | Al2O3= | 2.03  |             | 比较       | 2.89692756  |
| 计划日产  | 6500   |        | 参考日产   |        | 5255.6724 |        | 比较    | -1244.3276 |        | 参考加水量  |        | 11     | t/h    | 金属收得率 |       | 99%    | 出矿率   | 85%         | 作业率      | 100.00%     |



*   **样式**：表头背景深蓝（#1E3A8A），文字白色；表体行交替背景（#FFFFFF/#F8FAFC），文字深灰（#1F2937）；边框 1px 实线（#E5E7EB），无外间距；

*   **交互**：点击 “编辑” 可修改行内所有数值；支持批量导入（按钮 “导入原料数据”）、导出（按钮 “导出原料数据”）；表格支持横向滚动（内容超宽时）。

#### 2. 优化计算区（步骤 1：输入参数）



*   **位置**：物料信息表正下方，占页面剩余高度，横向分 3 个区域（无间隙，宽度占比 3:3:4），区域间用 1px 实线（#E5E7EB）分隔；

*   **整体样式**：背景浅灰（#F9FAFB），内边距 20px，区域标题为 16px 粗体（#1E3A8A），下方加 1px 橙色下划线（长度与标题同）。

##### （1）区域 1：原料数据库详细参数（补充输入）



*   **标题**：“原料数据库补充参数”；

*   **内容**（每项含标签 + 输入框，标签 100px 宽，输入框 200px 宽，纵向间距 10px）：


    *   原料水分补充：针对表格中未完善的原料，单独输入 “\[原料名] 物理水（%）：\_\_\_\_\_\_”（输入框，数值型，保留 2 位小数）；

    *   原料烧损率补充：“\[原料名] 烧损率（%）：\_\_\_\_\_\_”（输入框，数值型，保留 2 位小数）；

    *   原料单价补充：“\[原料名] 单价（元 / 吨）：\_\_\_\_\_\_”（输入框，数值型，保留 2 位小数）；

    *   说明文字：“\* 未填写时默认使用物料信息表中数据”（12px，#6B7280）；

*   **交互**：输入框聚焦时边框变橙色（#F97316），失焦时校验数值合法性（非负、≤100（水分 / 烧损率））。

##### （2）区域 2：配料优化目标



*   **标题**：“配料优化目标”；

*   **内容**：


    *   单选按钮组（横向排列，间距 20px）：


        *   “成本最优”（默认选中）：按钮旁加说明 “\* 优先降低吨矿成本，满足约束条件”；

        *   “质量最优”：按钮旁加说明 “\* 优先缩小成分偏差，满足约束条件”；

    *   算法选择（默认选中 “SQP 二次序列算法”）：


        *   下拉框：“优化算法：\_\_\_\_\_\_”（选项：“SQP 二次序列算法”“其他算法（预留）”）；

        *   算法信息提示：“\* 当前采用 Python 实现的 SQP 算法，计算精度 ±0.01”（12px，#6B7280）；

*   **交互**：选中项按钮背景为橙色（#F97316），文字白色，未选中为浅灰背景（#E5E7EB），文字深灰。

##### （3）区域 3：约束条件



*   **标题**：“约束条件设置”；

*   **内容**（分 4 组，组间纵向间距 15px，每组内标签 + 输入框横向排列）：


    *   组 1：成分范围约束（目标成分设定）


        *   “TFe 目标范围（%）：\_\_\_\_\_\_ \~ \_\_\_\_\_\_”（2 个输入框，数值型，如 54.0\~56.0）；

        *   “SiO₂目标范围（%）：\_\_\_\_\_\_ \~ \_\_\_\_\_\_”（输入框，参考值 4.8\~5.3）；

        *   “CaO 目标范围（%）：\_\_\_\_\_\_ \~ \_\_\_\_\_\_”（输入框，数值型）；

        *   “Al₂O₃上限（%）：\_\_\_\_\_\_”（输入框，默认 1.8）；

        *   “MgO 目标范围（%）：\_\_\_\_\_\_ \~ \_\_\_\_\_\_”（输入框，参考值 1.8\~2.2）；

        *   “P 上限（%）：**”“S 上限（%）：**”（输入框，数值型，≤0.1）。

    *   组 2：碱度（Ro）约束


        *   单选按钮：“Ro 计算方式：□ Ro=CaO/SiO₂ □ Ro=(CaO+MgO)/(SiO₂+Al₂O₃)”（默认选第一个）；

        *   “Ro 目标范围：\_\_\_\_\_\_ \~ \_\_\_\_\_\_”（输入框，参考 1.8\~2.4）。

    *   组 3：湿配比比例约束


        *   “\[原料名] 湿配比上限（%）：\_\_\_\_\_\_”（输入框，数值型，≤100，所有原料总和≤100）；

        *   “\[原料名] 湿配比下限（%）：\_\_\_\_\_\_”（输入框，数值型，≥0）；

        *   批量设置按钮：“统一设置湿配比范围（0\~\[X]%）”（输入框 + 按钮，X≤100）。

    *   组 4：成本与质量目标


        *   成本目标（仅成本最优时生效）：“目标成本上限（元 / 吨）：\_\_\_\_\_\_”（输入框，数值型）；

        *   质量目标（仅质量最优时生效）：“TFe 最大偏差（%）：**”“Ro 最大偏差（%）：**”（输入框，数值型，如 ±0.2）；

    *   计算按钮：底部居中，“开始计算”（宽 150px，高 40px，橙色背景 + 白色文字，hover 时背景加深）。

### （三）主页面 2：优化结果界面



*   **位置**：导航栏下方，整体占页面 100% 高度，横向分 2 个区域（无间隙，宽度占比 6:4），区域间用 1px 实线（#E5E7EB）分隔；

#### 1. 区域 1：优化结果表格



*   **标题**：“优化计算结果”（18px 粗体，#1E3A8A，顶部 10px 处）；

*   **内容**：


    *   标签页切换（“成本最优结果”“质量最优结果”，标签宽 150px，高 30px，选中标签橙色背景 + 白色文字，未选中浅灰背景 + 深灰文字）；

    *   成本最优结果表（标签 1 内容）：



| 列名        | 宽度    | 说明                           |
| --------- | ----- | ---------------------------- |
| 方案        | 80px  | 1、2、3...（自动编号）               |
| 湿配比（%）    | 250px | 如 “铁矿 A:45, 石灰石：15...”（换行显示） |
| 成本（元 / 吨） | 120px | 数值，保留 1 位小数                  |
| TFe（%）    | 100px | 数值，保留 1 位小数                  |
| R         | 100px | 数值，保留 2 位小数                  |
| 操作        | 100px | “查看详情” 按钮                    |



*   质量最优结果表（标签 2 内容）：



| 列名        | 宽度    | 说明             |
| --------- | ----- | -------------- |
| 方案        | 80px  | 1、2、3...（自动编号） |
| 湿配比（%）    | 250px | 同成本表           |
| TFe 偏差（%） | 120px | 带正负号，如 “+0.1”  |
| R 偏差（%）   | 120px | 带正负号，如 “-0.05” |
| MgO（%）    | 100px | 数值，保留 1 位小数    |
| 操作        | 100px | “查看详情” 按钮      |



*   表格样式：同物料信息表，表头背景深蓝，表体交替行背景，支持横向 / 纵向滚动（内容超宽 / 高时）；

*   交互：点击 “查看详情” 弹出抽屉（右侧滑出），展示该方案的详细计算过程（干料量、烧成量、各成分总和等中间数据）。

#### 2. 区域 2：算法信息与辅助数据



*   **标题**：“算法与计算说明”（16px 粗体，#1E3A8A，顶部 10px 处）；

*   **内容**：


    *   算法信息：


        *   “优化算法：SQP 二次序列算法”（14px，#1F2937）；

        *   “算法精度：计算偏差≤0.02%（成分）、≤0.01（碱度）”；

        *   “计算逻辑：基于干料量 = 配料比 ×(1 - 物理水)、烧成量 = 干料量 ×(1 - 烧损率) 推导，各成分含量 =Σ(原料成分 × 干料量)/Σ 烧成量 ×100”（12px，#4B5563，换行显示）；

    *   关键指标计算公式（折叠面板，默认展开）：


        *   标题 “关键指标计算公式”（14px 粗体）；

        *   内容：


            *   “干料量 = 配料比（%）×（1 - 物理水（%）/100）”；

            *   “总干料量 =Σ 各原料干料量”；

            *   “烧成量 = 干料量 ×（1 - 烧损率（%）/100）”；

            *   “TFe（%）=Σ（原料干料量 × 原料 TFe（%））/Σ 烧成量 ×100”；

            *   “Ro=ΣCaO/ΣSiO₂ 或 (ΣCaO+ΣMgO)/(ΣSiO₂+ΣAl₂O₃)”；

    *   结果导出按钮：“导出结果数据（Excel）”（宽 150px，高 30px，橙色边框 + 橙色文字，hover 时背景变浅橙）。

## 五、通用组件要求



1.  输入框：统一样式（高 30px，边框 1px 实线 #E5E7EB，圆角 3px，内边距 5px），支持数值型（带上下箭头微调）、文本型；

2.  按钮：统一高 36px，圆角 3px，文字 14px，hover 时阴影加深（0 4px 6px rgba (0,0,0,0.1)）；

3.  表格：固定表头，内容区可滚动，行高 40px，文字居中对齐；

4.  提示信息：错误提示（红色 #EF4444，在输入框下方）、成功提示（绿色 #10B981，顶部浮动），自动消失（3 秒）。

## 六、交互要求



1.  页面切换：导航栏按钮点击后，主页面内容无刷新切换（平滑过渡，动画时长 0.3 秒）；

2.  计算流程：点击 “开始计算” 后，显示加载动画（橙色旋转圆圈），计算完成后自动切换到 “优化结果” 页面；

3.  数据联动：物料信息表中原料数据修改后，优化计算区的 “原料名” 下拉选项实时更新；

4.  约束校验：输入参数时实时校验（如湿配比总和超 100% 时，即时提示 “湿配比总和不能超过 100%”）。

## 七、注意事项



1.  所有模块横向排列，无间隙（margin=0，padding 按设计预留，确保视觉无缝）；

2.  物料信息表为核心基础表，需支持 50 + 行数据展示，可通过滚动查看；

3.  文字内容不折行（表格列宽自适应，超宽时横向滚动）；

4.  整体风格保持工业软件专业性，避免冗余装饰，突出数据与操作效率。

> （注：文档部分内容可能由 AI 生成）