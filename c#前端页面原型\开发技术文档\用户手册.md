# 烧结矿成分及指标计算系统 - 用户手册

## 目录
1. [系统概述](#系统概述)
2. [系统安装](#系统安装)
3. [界面介绍](#界面介绍)
4. [操作指南](#操作指南)
5. [常见问题](#常见问题)
6. [技术支持](#技术支持)

## 系统概述

烧结矿成分及指标计算系统是一套基于SQP二次序列规划算法的烧结配料优化系统，旨在帮助钢铁企业实现烧结配料的成本最优和质量最优双目标优化。

### 主要功能
- **原料数据管理**: 维护和管理各种原料的成分信息
- **配料优化计算**: 基于SQP算法进行配料比例优化
- **结果分析展示**: 直观展示优化结果和关键指标
- **数据导入导出**: 支持Excel/CSV格式的数据交换

### 系统特点
- 工业级用户界面设计
- 高精度优化算法
- 实时计算反馈
- 多方案对比分析

## 系统安装

### 环境要求
- **操作系统**: Windows 10/11 (64位)
- **内存**: 4GB以上
- **硬盘空间**: 500MB以上
- **网络**: 本地网络连接

### 安装步骤

#### 1. 安装运行环境
1. 下载并安装 [.NET 6.0 Runtime](https://dotnet.microsoft.com/download/dotnet/6.0)
2. 下载并安装 [Python 3.8+](https://www.python.org/downloads/)

#### 2. 部署系统文件
1. 解压系统安装包到目标目录
2. 双击运行 `start_system.bat` 启动脚本
3. 系统将自动检查环境并启动服务

#### 3. 验证安装
- 系统启动后会自动进行连接测试
- 看到"🎉 系统启动完成!"表示安装成功

## 界面介绍

### 主界面布局

系统采用横向无间隙布局设计，主要包含：

#### 1. 导航栏（顶部）
- **系统Logo**: 位于左侧，显示系统标识
- **系统名称**: "烧结矿成分及指标计算系统"
- **页面切换按钮**: "优化计算"和"优化结果"两个主要页面

#### 2. 优化计算界面
- **物料信息表**: 显示和编辑原料基础数据
- **原料数据库参数**: 补充原料的详细参数
- **配料优化目标**: 选择优化类型和算法
- **约束条件设置**: 设置各种约束条件

#### 3. 优化结果界面
- **结果表格**: 展示优化计算结果
- **算法信息**: 显示算法详情和计算公式
- **方案详情**: 查看具体方案的详细信息

## 操作指南

### 第一步：原料数据管理

#### 查看原料信息
1. 启动系统后默认显示优化计算界面
2. 上方的物料信息表显示所有原料数据
3. 表格包含原料名称、成分含量、价格等信息

#### 编辑原料数据
1. 点击表格中的原料行选中
2. 点击"编辑"按钮进入编辑模式
3. 修改相应的数值
4. 点击"保存"确认修改

#### 添加新原料
1. 点击"添加原料"按钮
2. 输入原料名称和各项成分数据
3. 设置价格和配比约束
4. 保存新原料信息

#### 导入/导出数据
1. 点击"导入原料数据"从CSV文件导入
2. 点击"导出原料数据"保存为CSV文件
3. 支持Excel格式的数据交换

### 第二步：设置优化参数

#### 选择优化目标
1. 在"配料优化目标"区域选择：
   - **成本最优**: 优先降低吨矿成本
   - **质量最优**: 优先缩小成分偏差
2. 确认算法选择（默认SQP算法）

#### 设置约束条件
1. **成分范围约束**:
   - 设置TFe、SiO₂、CaO等目标范围
   - 设置Al₂O₃、P、S的上限值
   
2. **碱度约束**:
   - 选择碱度计算方式
   - 设置Ro目标范围

3. **配比约束**:
   - 设置各原料的最小/最大配比
   - 可使用"统一设置"功能批量设置

4. **成本与质量目标**:
   - 成本最优时设置成本上限
   - 质量最优时设置偏差限制

### 第三步：执行优化计算

#### 开始计算
1. 检查所有参数设置是否合理
2. 点击"验证约束"检查约束条件
3. 点击"开始计算"启动优化
4. 观察计算进度和状态信息

#### 查看结果
1. 计算完成后自动跳转到结果页面
2. 在"成本最优结果"和"质量最优结果"间切换
3. 查看最优配比和烧结矿成分
4. 点击"查看详情"获取详细信息

### 第四步：结果分析

#### 方案对比
1. 对比不同优化目标的结果
2. 分析成本与质量的权衡
3. 选择最适合的配料方案

#### 导出结果
1. 点击"导出结果数据（Excel）"
2. 选择保存位置和文件格式
3. 生成详细的计算报告

## 常见问题

### Q1: 系统启动失败怎么办？
**A**: 
1. 检查.NET 6.0和Python是否正确安装
2. 确认端口5000未被其他程序占用
3. 以管理员权限运行启动脚本
4. 查看控制台错误信息

### Q2: 优化计算失败的原因？
**A**:
1. **约束条件过严**: 放宽约束范围
2. **原料数据不完整**: 检查必要的成分数据
3. **配比约束冲突**: 确保最小配比总和≤100%
4. **目标值不合理**: 调整目标成分范围

### Q3: 如何提高计算精度？
**A**:
1. 提供更准确的原料成分数据
2. 合理设置约束条件范围
3. 增加原料种类提供更多选择
4. 根据实际生产调整目标值

### Q4: 结果与实际生产差异较大？
**A**:
1. 检查原料实际成分与输入数据的一致性
2. 考虑生产过程中的损失和变化
3. 根据实际情况调整烧损率等参数
4. 定期更新原料价格信息

### Q5: 如何备份和恢复数据？
**A**:
1. 使用"导出原料数据"功能备份原料信息
2. 保存优化结果到Excel文件
3. 定期备份整个系统目录
4. 使用"导入原料数据"恢复数据

## 操作技巧

### 提高效率的建议
1. **批量设置**: 使用"统一设置配比范围"快速设置约束
2. **模板保存**: 将常用的参数设置导出为模板
3. **结果对比**: 同时查看多种优化方案进行对比
4. **定期更新**: 及时更新原料价格和成分数据

### 最佳实践
1. **数据准确性**: 确保原料数据的准确性和时效性
2. **约束合理性**: 设置符合实际生产的约束条件
3. **结果验证**: 将优化结果与实际生产进行对比验证
4. **持续改进**: 根据生产反馈不断优化参数设置

## 技术支持

### 联系方式
- **技术支持邮箱**: <EMAIL>
- **用户手册更新**: 请关注系统更新通知
- **培训服务**: 可提供现场培训和远程指导

### 故障报告
报告问题时请提供：
1. 操作系统版本信息
2. 错误信息截图
3. 详细的操作步骤
4. 输入数据示例

### 版本更新
- 系统会自动检查更新
- 重要更新会有邮件通知
- 建议定期更新到最新版本

---

**注意**: 本手册基于系统v1.0.0版本编写，如有更新请以最新版本为准。
